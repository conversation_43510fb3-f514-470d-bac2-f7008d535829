<script>
    import { getImmaginiServizio } from '@lib/supabase/storage';
    import Icon from '@iconify/svelte';
    import { onMount } from 'svelte';
    import { t } from '@lib/i18n';

    export let servizio;
    let expandedImageIndex = null;


    const toggleImageExpansion = (index) => {
        if (expandedImageIndex === index) {
            expandedImageIndex = null;
        } else {
            expandedImageIndex = index;
        }
    };

    const closeExpansion = () => {
        expandedImageIndex = null;
    };

</script>

<ion-grid class="ion-padding main-grid">
    <!-- TITLE -->
    <ion-row>
        <ion-col>
            <ion-text class="h1">{$t('photos_of')} {servizio.nome}</ion-text>
        </ion-col>
    </ion-row>

    <!-- PHOTOS -->
    <ion-row>
        {#each servizio.foto as immagine, index}
            <ion-col size={6} class:expanded={expandedImageIndex === index}>
                <div
                    class="photo-card"
                    class:expanded={expandedImageIndex === index}
                    on:click={() => toggleImageExpansion(index)}
                >
                    <ion-img
                        loading="lazy"
                        src={immagine}
                        alt={$t('service_photo')}
                        on:ionError={(e) => {
                            e.target.src = 'placeholder.jpg';
                        }}
                    />
                    <div class="photo-overlay" class:expanded={expandedImageIndex === index}>
                        <Icon
                            icon={expandedImageIndex === index ? "ion:contract-outline" : "ion:expand-outline"}
                            width={24}
                            color="#ffffff"
                        />
                    </div>
                </div>
            </ion-col>
        {/each}
    </ion-row>
</ion-grid>

<!-- Backdrop for closing expanded image -->
{#if expandedImageIndex !== null}
    <div class="expansion-backdrop" on:click={closeExpansion}></div>
{/if}

<style>
    .photo-card {
        position: relative;
        width: 100%;
        height: 160px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        z-index: 1;
    }

    .photo-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .photo-card.expanded {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90vw;
        height: 70vh;
        max-width: 800px;
        max-height: 600px;
        z-index: 1000;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        border-radius: 12px;
    }

    ion-col.expanded {
        z-index: 1000;
    }

    .photo-card ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: object-fit 0.3s ease;
    }

    .photo-card.expanded ion-img {
        object-fit: contain;
    }

    .photo-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.2s ease;
    }

    .photo-card:hover .photo-overlay,
    .photo-overlay.expanded {
        opacity: 1;
    }

    .expansion-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);
        z-index: 999;
        backdrop-filter: blur(3px);
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
        .photo-card {
            height: 140px;
        }

        .photo-card.expanded {
            width: 95vw;
            height: 60vh;
            border-radius: 8px;
        }
    }

    @media (min-width: 768px) {
        .photo-card {
            height: 180px;
        }
    }

    /* Prevent body scroll when image is expanded */
    :global(body:has(.photo-card.expanded)) {
        overflow: hidden;
    }
</style>
<script>
    import { onMount, createEventDispatcher } from 'svelte';
    import Icon from '@iconify/svelte';
    import { t } from '@lib/i18n';

    export let images = [];
    export let initialIndex = 0;

    const dispatch = createEventDispatcher();
    let currentIndex = initialIndex;
    let touchStartX = 0;
    let touchEndX = 0;

    // Function to handle image navigation
    const nextImage = () => {
        if (images.length > 1) {
            currentIndex = (currentIndex + 1) % images.length;
        }
    };

    const prevImage = () => {
        if (images.length > 1) {
            currentIndex = (currentIndex - 1 + images.length) % images.length;
        }
    };

    // Touch handlers for swipe gestures
    const handleTouchStart = (e) => {
        touchStartX = e.touches[0].clientX;
    };

    const handleTouchEnd = (e) => {
        touchEndX = e.changedTouches[0].clientX;
        handleSwipe();
    };

    const handleSwipe = () => {
        const swipeThreshold = 50; // Minimum distance to be considered a swipe
        const swipeDistance = touchEndX - touchStartX;

        if (Math.abs(swipeDistance) < swipeThreshold) return;

        if (swipeDistance > 0) {
            // Swipe right -> previous image
            prevImage();
        } else {
            // Swipe left -> next image
            nextImage();
        }
    };

    // Close the modal
    const closeModal = () => {
        dispatch('close');
    };

    // Select a specific image
    const selectImage = (index) => {
        currentIndex = index;
    };

    // Handle keyboard navigation
    const handleKeydown = (e) => {
        if (e.key === 'Escape') {
            closeModal();
        } else if (e.key === 'ArrowRight') {
            nextImage();
        } else if (e.key === 'ArrowLeft') {
            prevImage();
        }
    };

    onMount(() => {
        // Add keyboard event listener
        window.addEventListener('keydown', handleKeydown);

        // Clean up when component is destroyed
        return () => {
            window.removeEventListener('keydown', handleKeydown);
        };
    });
</script>

<div
    class="modal-backdrop"
    on:click={closeModal}
>
    <div class="modal-content" on:click|stopPropagation
        on:touchstart={handleTouchStart}
        on:touchend={handleTouchEnd}
    >
        <!-- Close button -->
        <button class="close-button" on:click={closeModal} aria-label="Close">
            <Icon icon="ion:close" width={30} color="#ffffff" />
        </button>

        <!-- Main image container -->
        <div class="main-image-container">
            <img
                src={images[currentIndex]}
                alt={$t('gallery_image')}
                class="main-image"
                on:error={(e) => {
                    e.target.src = 'placeholder.jpg';
                }}
            />

            <!-- Navigation buttons -->
            {#if images.length > 1}
                <button class="nav-button prev" on:click|stopPropagation={prevImage}>
                    <Icon icon="ion:chevron-back" width={24} color="#ffffff" />
                </button>

                <button class="nav-button next" on:click|stopPropagation={nextImage}>
                    <Icon icon="ion:chevron-forward" width={24} color="#ffffff" />
                </button>
            {/if}
        </div>

        <!-- Thumbnails -->
        {#if images.length > 1}
            <div class="thumbnails-container">
                {#each images as image, i}
                    <div
                        class="thumbnail"
                        class:active={i === currentIndex}
                        on:click|stopPropagation={() => selectImage(i)}
                    >
                        <img
                            src={image}
                            alt={$t('thumbnail')}
                            on:error={(e) => {
                                e.target.src = 'placeholder.jpg';
                            }}
                        />
                    </div>
                {/each}
            </div>
        {/if}
    </div>
</div>

<style>
    .modal-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        backdrop-filter: blur(3px);
        z-index: 9999;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        overflow: hidden;
    }

    .modal-content {
        position: relative;
        width: 90%;
        max-width: 1200px;
        height: auto;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding: 20px;
        box-sizing: border-box;
        background-color: white;
        border-radius: 25px;
        box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
        overflow: hidden;
    }

    .close-button {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: var(--ion-color-primary);
        border: none;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 100;
        padding: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .main-image-container {
        position: relative;
        width: 100%;
        height: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        border-radius: 8px;
        padding: 5px;
    }

    .main-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 8px;
    }

    .nav-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: var(--ion-color-medium);
        border: none;
        border-radius: 50%;
        width: 44px;
        height: 44px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .nav-button.prev {
        left: 15px;
    }

    .nav-button.next {
        right: 15px;
    }

    .thumbnails-container {
        display: flex;
        overflow-x: auto;
        gap: 10px;
        padding: 15px 10px;
        width: 100%;
        max-width: 100%;
        justify-content: center;
        height: 90px;
        scrollbar-width: thin;
        scrollbar-color: var(--ion-color-medium) var(--ion-color-light);
    }

    .thumbnails-container::-webkit-scrollbar {
        height: 6px;
    }

    .thumbnails-container::-webkit-scrollbar-track {
        background: var(--ion-color-light-shade);
        border-radius: 3px;
    }

    .thumbnails-container::-webkit-scrollbar-thumb {
        background-color: var(--ion-color-medium);
        border-radius: 3px;
    }

    .thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 10px;
        overflow: hidden;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s, transform 0.2s, box-shadow 0.2s;
        flex-shrink: 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .thumbnail.active {
        opacity: 1;
        transform: scale(1.1);
        box-shadow: 0 0 0 2px var(--ion-color-primary), 0 3px 5px rgba(0, 0, 0, 0.2);
    }

    .thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            padding: 15px 10px;
        }

        .close-button {
            top: 5px;
            right: 5px;
            width: 40px;
            height: 40px;
        }

        .main-image-container {
            height: 300px;
            padding: 3px;
            border-radius: 12px;
        }

        .thumbnails-container {
            padding: 10px 5px;
            height: 80px;
            border-radius: 12px;
        }

        .thumbnail {
            width: 50px;
            height: 50px;
            border-radius: 8px;
        }

        .nav-button {
            width: 36px;
            height: 36px;
        }

        .nav-button.prev {
            left: 10px;
        }

        .nav-button.next {
            right: 10px;
        }
    }

    @media (max-width: 480px) {
        .main-image-container {
            height: 250px;
        }
    }
</style>

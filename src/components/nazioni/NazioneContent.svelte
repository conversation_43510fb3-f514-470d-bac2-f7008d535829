<script>
    import { selectServiziByNazione, getAnteprimaServizio } from '@lib/supabase';
    import { navController } from 'ionic-svelte';
    import Servi<PERSON> from '@pages/Servizio.svelte';
    import Icon from '@iconify/svelte';
    import { onMount } from 'svelte';

    export let categoria;
    let servizi = [];
    let infiniteScroll;
    let isLoading;

    onMount(async () => {
        isLoading = true;
        servizi = await selectServiziByNazione(categoria.nome);

        for (const servizio of servizi) {
            servizio.orario = servizio.orari[0];
            servizio.indirizzo = servizio.indirizzi[0];

            const imageUrl = await getAnteprimaServizio(servizio.nome);
            servizio.anteprima = imageUrl;
        }
        isLoading = false;
    })
    
    const getOrariOfTheDay = (orariServizio) => {
        const giorniSettimana = ["domenica", "lunedì", "martedì", "mercoledì", "gioved<PERSON>", "venerd<PERSON>", "sabato"];
        const giorno = giorniSettimana[new Date().getDay()];
        return orariServizio[giorno];
    }

    const getIndirizzo = (indirizzoServizio) => {
        return `${indirizzoServizio.via}${indirizzoServizio.numero_civico ? ', ' + indirizzoServizio.numero_civico : ''}${indirizzoServizio.cap ? ', ' + indirizzoServizio.cap : ''}${indirizzoServizio.città ? ', ' + indirizzoServizio.città : ''}`;
    }
</script>

<ion-grid class="ion-padding main-grid">
    <!-- TITLE -->
    <ion-row>
        <ion-col>
            <ion-text class="h1">Il Salento in {categoria.nome}</ion-text>
        </ion-col>
    </ion-row>

    <ion-row>
        <!-- SERVIZI -->
        {#if isLoading}
            {#each Array(2) as _}
                <ion-col size={12}>
                    <ion-card>
                        <ion-skeleton-text animated style="height: 140px; margin: 0;"/>
                        <ion-card-header>
                            <ion-row class="ion-align-items-center ion-justify-content-between">
                                <div style="display:flex; align-items: center; gap: 5px;">
                                    <ion-skeleton-text animated style="width: 60px; height: 20px; border-radius: 4px;"/>
                                    <ion-skeleton-text animated style="width: 80px; height: 20px; border-radius: 4px;"/>
                                </div>
                            </ion-row>
                            <ion-skeleton-text animated style="width: 70%; height: 24px; margin-top: 8px;"/>
                        </ion-card-header>
                        <ion-card-content>
                            <ion-grid class="ion-no-padding">
                                {#each Array(3) as _}
                                    <ion-row class="ion-align-items-center padding-bottom-3">
                                        <ion-col size={1}>
                                            <ion-skeleton-text animated style="width: 20px; height: 20px;"/>
                                        </ion-col>
                                        <ion-col>
                                            <ion-skeleton-text animated style="width: 60%;"/>
                                        </ion-col>
                                    </ion-row>
                                {/each}
                            </ion-grid>
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            {/each}
        {:else}
            {#each servizi as servizio}
                <ion-col size={12} on:click={() => navController.push(Servizio, {servizio})}>
                    <ion-card class:premium={servizio.premium}>
                        <ion-img 
                            loading="lazy" 
                            src={servizio.anteprima || 'placeholder.jpg'} 
                            alt="copertina"
                            on:ionError={(e) => {
                                e.target.src = 'placeholder.jpg';
                            }}
                        />
                        <div id="premium-icon" class="absolute-top-right" class:ion-hide={!servizio.premium}>
                            <Icon icon="ion:star" color="var(--ion-color-warning)" width={25}/>
                        </div>
                        <ion-card-header style:padding-bottom="8px">
                            <ion-row class="ion-align-items-center ion-justify-content-between" >
                                <div style="display:flex; align-items: center; gap: 5px;">
                                    <ion-badge>{servizio.tipologia}</ion-badge>
                                    <ion-badge color="secondary">{servizio.indirizzo.città}</ion-badge>
                                </div>
                                {#if servizio.premium}
                                    <ion-badge color="warning">Premium</ion-badge>
                                {/if}
                            </ion-row>
                        </ion-card-header>
                        <ion-card-content>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1;">
                                    <ion-card-title>{servizio.nome}</ion-card-title>
                                    <ion-grid class="ion-no-padding" style:color="black">
                                        <!-- INDIRIZZO -->
                                        <ion-row class="ion-align-items-center padding-bottom-3">
                                            <ion-col size={1}>
                                                <Icon icon="ion:location-outline" width={20}/>
                                            </ion-col>
                                            <ion-col>
                                                <ion-text>{getIndirizzo(servizio.indirizzo)}</ion-text>
                                            </ion-col>
                                        </ion-row>

                                        <!-- TELEFONO -->
                                        <!-- <ion-row class="ion-align-items-center padding-bottom-3">
                                            <ion-col size={1}>
                                                <Icon icon="ion:call-outline" width={20}/>
                                            </ion-col>
                                            <ion-col>
                                                {#if servizio.telefono_primario}
                                                    <ion-text>{servizio.telefono_primario.startsWith('+') ? servizio.telefono_primario : '+39 ' + servizio.telefono_primario}</ion-text>
                                                {:else}
                                                    <ion-text>Non disponibile</ion-text>
                                                {/if}
                                            </ion-col>
                                        </ion-row>   -->  
                                    </ion-grid>
                                </div>
                                <!-- <div style="flex: 0.3;">
                                    <div class="info-button">
                                        Clicca per dettagli
                                    </div>
                                </div> -->
                            </div>
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            {:else}
                <ion-col class="ion-padding">
                    <ion-text style:font-size="large">Non ci sono servizi disponibili per questa categoria</ion-text>
                </ion-col>
            {/each}
        {/if}
    </ion-row>
</ion-grid>

<style>
    ion-card {
        margin-top: 0px;
        margin-bottom: 8px;
        transition: all .5s ease-in-out;
    }

    ion-card.premium {
        box-shadow: 0 0 10px var(--ion-color-primary);
    }

    ion-img {
        height: 140px;
        object-fit: cover;
    }

    ion-card-title {
        font-size: x-large;
        padding-bottom: 5px;
    }

    ion-row.padding-bottom-3 {
        padding-bottom: 3px;
    }

    .absolute-top-right {
        position: absolute;
        top: 0;
        right: 0;
        margin: 10px;
    }


    :global(.md ion-label) {
        font-size: large;
    }

    .info-button {
        background: var(--ion-color-primary);
        width: 100%;
        padding: 5px;
        color: var(--ion-color-primary-contrast);
        text-align: center;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
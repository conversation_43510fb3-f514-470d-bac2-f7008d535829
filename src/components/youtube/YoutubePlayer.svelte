<script>
    import { onMount, onD<PERSON>roy, createEventDispatcher } from 'svelte';
    import { extractYoutubeVideoId } from '@lib/utils/youtube';
    import { toastController } from 'ionic-svelte';
    
    // Props
    export let videoUrl = '';
    export let videoId = '';
    export let width = '100%';
    export let height = '200px';
    export let autoplay = false;
    export let playerVars = {};
    
    // Internal state
    let player;
    let playerId = `youtube-player-${Math.random().toString(36).substring(2, 11)}`;
    let playerReady = false;
    let apiLoaded = false;
    let playerContainer;
    let playerError = false;
    
    const dispatch = createEventDispatcher();
    
    // Load YouTube API if not already loaded
    const loadYouTubeAPI = () => {
        if (window.YT && window.YT.Player) {
            apiLoaded = true;
            initPlayer();
            return;
        }
        
        // Create script tag if it doesn't exist
        if (!document.getElementById('youtube-iframe-api')) {
            const tag = document.createElement('script');
            tag.id = 'youtube-iframe-api';
            tag.src = 'https://www.youtube.com/iframe_api';
            const firstScriptTag = document.getElementsByTagName('script')[0];
            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
        }
        
        // Set up callback for when API is ready
        window.onYouTubeIframeAPIReady = () => {
            apiLoaded = true;
            initPlayer();
        };
    };
    
    // Initialize the player
    const initPlayer = () => {
        if (!apiLoaded || !playerContainer) return;
        
        // Get video ID from URL if not provided directly
        const videoIdToUse = videoId || extractYoutubeVideoId(videoUrl);
        
        if (!videoIdToUse) {
            console.error('No valid YouTube video ID provided');
            playerError = true;
            dispatch('error', { message: 'Invalid YouTube video URL' });
            return;
        }
        
        try {
            // Default player options
            const defaultOptions = {
                rel: 0,
                showinfo: 1,
                color: 'white',
                playsinline: 1,
                autoplay: autoplay ? 1 : 0
            };
            
            // Create player
            player = new window.YT.Player(playerId, {
                width: width,
                height: height,
                videoId: videoIdToUse,
                playerVars: { ...defaultOptions, ...playerVars },
                events: {
                    'onReady': onPlayerReady,
                    'onStateChange': onPlayerStateChange,
                    'onError': onPlayerError
                }
            });
        } catch (error) {
            console.error('Error initializing YouTube player:', error);
            playerError = true;
            dispatch('error', { message: 'Failed to initialize YouTube player' });
        }
    };
    
    // Player event handlers
    const onPlayerReady = (event) => {
        playerReady = true;
        dispatch('ready', { player: player });
    };
    
    const onPlayerStateChange = (event) => {
        dispatch('stateChange', { state: event.data });
    };
    
    const onPlayerError = (event) => {
        console.error('YouTube player error:', event);
        playerError = true;
        dispatch('error', { code: event.data });
        
        // Show error toast
        showErrorToast('Errore durante la riproduzione del video');
    };
    
    const showErrorToast = async (message) => {
        const toast = await toastController.create({
            message: message,
            duration: 2000,
            position: 'bottom',
            color: 'danger'
        });
        await toast.present();
    };
    
    // Public methods
    export const playVideo = () => {
        if (player && playerReady) {
            player.playVideo();
        }
    };
    
    export const pauseVideo = () => {
        if (player && playerReady) {
            player.pauseVideo();
        }
    };
    
    export const stopVideo = () => {
        if (player && playerReady) {
            player.stopVideo();
        }
    };
    
    export const seekTo = (seconds, allowSeekAhead = true) => {
        if (player && playerReady) {
            player.seekTo(seconds, allowSeekAhead);
        }
    };
    
    // Lifecycle hooks
    onMount(() => {
        loadYouTubeAPI();
    });
    
    onDestroy(() => {
        if (player) {
            try {
                player.destroy();
            } catch (error) {
                console.error('Error destroying YouTube player:', error);
            }
        }
    });
    
    // Watch for changes to videoUrl or videoId
    $: if (apiLoaded && (videoUrl || videoId) && player) {
        const newVideoId = videoId || extractYoutubeVideoId(videoUrl);
        if (newVideoId && playerReady) {
            player.loadVideoById(newVideoId);
        }
    }
</script>

<div class="youtube-player-container" bind:this={playerContainer}>
    {#if playerError}
        <div class="youtube-player-error">
            <p>Impossibile caricare il video</p>
        </div>
    {:else}
        <div id={playerId}></div>
    {/if}
</div>

<style>
    .youtube-player-container {
        width: 100%;
        position: relative;
        overflow: hidden;
        border-radius: 12px;
    }
    
    .youtube-player-error {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f8f8;
        color: #666;
        height: 200px;
        text-align: center;
        border-radius: 12px;
    }
</style>

<script>
    import { navController } from "ionic-svelte";
    import { selectArtisti, getAnteprimaArtista } from '@lib/supabase';
    import { onMount } from 'svelte';
    import Icon from '@iconify/svelte';
    import { ARTISTA_MODAL_OPEN, ARTISTA_MODAL_PROPS } from '@store';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    export let categoria;
    let artisti = categoria?.artisti;
    export let isLoading = true;

    onMount(async () => {
        isLoading = true;
    
        for (const artista of artisti) {
            const imageUrl = await getAnteprimaArtista(artista.nome);
            artista.anteprima = imageUrl;
        }
        
        isLoading = false;
    })
</script>

<ion-grid class="ion-padding main-grid">
  <!-- TITOLO -->
  <ion-row class="section">
      <ion-col>
          <ion-text class="h1">{categoria.nome}</ion-text>
      </ion-col>
  </ion-row>

  <ion-row>
    <!-- ARTISTI -->
    {#if isLoading}
    <ion-col size="12">
      <div class="skeleton-card">
        <!-- Skeleton structure mimicking the artist card -->
        <div class="skeleton-image"></div>
        <div class="skeleton-title"></div>
        <div class="skeleton-description"></div>
      </div>
    </ion-col>
    {:else}
        {#each artisti as artista}
          <ion-col size="12">
              <ion-card on:click={() => {
                  $ARTISTA_MODAL_PROPS = { artista };
                  ARTISTA_MODAL_OPEN.set(true);
              }}>
                  <ion-img src={artista.anteprima} alt={artista.nome} />
                  <ion-card-header>
                      <div class="title-row">
                          <ion-card-title>{artista.nome}</ion-card-title>
                          {#if artista.data_nascita}
                              <div class="birth-date">
                                  <Icon icon="ion:calendar-outline" width={20}/>
                                  <ion-text>
                                      {new Date(artista.data_nascita).toLocaleDateString('it-IT', {
                                          day: '2-digit',
                                          month: '2-digit',
                                          year: 'numeric'
                                      })}
                                  </ion-text>
                              </div>
                          {/if}
                      </div>
                      <ion-card-subtitle class="biografia">
                          {parseMultiLanguageContent(artista.biografia)}
                      </ion-card-subtitle>
                  </ion-card-header>
              </ion-card>
          </ion-col>
        {/each}
    {/if}
  </ion-row>
</ion-grid>

<style>
    ion-card {
        margin-top: 0px;
        margin-bottom: 8px;
        transition: all .5s ease-in-out;
    }

    ion-img {
        height: 140px;
        object-fit: cover;
    }

    .title-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 5px;
    }

    .birth-date {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--ion-color-medium);
        font-size: medium;
    }

    ion-card-title {
        margin: 0;
        font-size: x-large;
    }

    ion-card-subtitle.biografia {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }

  .skeleton-card {
    background: #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    margin: 8px;
    display: flex;
    flex-direction: column;
  }
  .skeleton-image {
    width: 100%;
    height: 150px;
    background: #c0c0c0;
    border-radius: 4px;
    margin-bottom: 8px;
  }
  .skeleton-title {
    width: 60%;
    height: 20px;
    background: #c0c0c0;
    border-radius: 4px;
    margin-bottom: 4px;
  }
  .skeleton-description {
    width: 80%;
    height: 16px;
    background: #c0c0c0;
    border-radius: 4px;
  }
</style>

<script>
    import { selectListaCategorieArtisti } from "@lib/supabase/database";
    import { getAnteprimaCategoriaArtisti } from "@lib/supabase/storage";
    import CategoriaArtisti from "@pages/CategoriaArtisti.svelte";
    import { navController } from "ionic-svelte";
    import { onMount } from "svelte";

    let categorieArtisti = [];
    let isLoading;
    onMount(async () => {
        isLoading = true;
        categorieArtisti = await selectListaCategorieArtisti();
        
        for (const categoriaArtisti of categorieArtisti) {
            const imageUrl = await getAnteprimaCategoriaArtisti(categoriaArtisti.nome);
            categoriaArtisti.anteprima = imageUrl;
        }
        
        isLoading = false;
    });
</script>

<ion-grid class="ion-padding main-grid">
    <!-- TITOLO -->
    <ion-row>
        <ion-col>
            <ion-text class="h1">Categorie Artisti</ion-text>
        </ion-col>
    </ion-row>
    <ion-row>
        {#if isLoading}
            {#each Array(6) as _}
                <ion-col size="6">
                    <ion-card style="aspect-ratio: 1; margin: 2px;">
                        <ion-skeleton-text animated style="width: 100%; height: 100%;"/>
                    </ion-card>
                </ion-col>
            {/each}
        {:else}
            {#each categorieArtisti as categoriaArtisti}
                <ion-col size="6">
                    <ion-card class="categoria-artisti" on:click={() => navController.push(CategoriaArtisti, {categoria: categoriaArtisti})}>
                        <div class="button">
                            <ion-img loading="lazy" src={categoriaArtisti.anteprima || 'placeholder.jpg'} alt={categoriaArtisti.nome}/>
                        </div>
                        <div class="text">
                            <ion-text class="titolo">{categoriaArtisti.nome}</ion-text>
                        </div>
                    </ion-card>
                </ion-col>
            {/each}
        {/if}
    </ion-row>
</ion-grid>

<style>
    ion-card.categoria-artisti {
        aspect-ratio: 1;
        height: auto;
        margin: 2px;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
    }

    ion-card.categoria-artisti > div.button {
        flex: 0.9;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: center;
        overflow: hidden;
        position: relative;
    }

    ion-card.categoria-artisti > div.button > ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.categoria-artisti > div.button > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.categoria-artisti .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 8px 12px;
        flex: 0.3;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    ion-card.categoria-artisti .text .titolo {
        font-size: medium;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }
</style>

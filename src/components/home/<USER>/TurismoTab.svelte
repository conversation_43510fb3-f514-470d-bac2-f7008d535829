<script>
    import { selectCategoriePerIlTurista } from '@lib/supabase/database';
    import { getAnteprimaCategoria } from '@lib/supabase/storage';
    import Categoria from '@pages/Categoria.svelte';
    import { navController } from 'ionic-svelte';
    import { onMount } from 'svelte';
    import { t } from '@lib/i18n';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    let categorie = [];
    let isLoading;

    onMount(async () => {
        isLoading = true;

        categorie = await selectCategoriePerIlTurista();
        for (const categoria of categorie) {
            const imageUrl = await getAnteprimaCategoria(categoria.nome);
            if (imageUrl) {
                categoria.anteprima = imageUrl;
            }
        }
        isLoading = false;
    })
</script>

<!-- TITOLO -->
<ion-row>
    <ion-col>
        <ion-text class="h1">{$t('tourist_title')}</ion-text>
    </ion-col>
</ion-row>

<!-- DESCRIZIONE -->
<ion-row>
    <ion-col>
        <ion-text style:font-size="large">{$t('tourist_description')}</ion-text>
    </ion-col>
</ion-row>

<!-- CATEGORIE -->
<ion-row>
    {#if isLoading}
        {#each Array(6) as _}
            <ion-col size="6">
                <ion-card style="aspect-ratio: 1; margin: 2px;">
                    <ion-skeleton-text animated style="width: 100%; height: 100%;"/>
                </ion-card>
            </ion-col>
        {/each}
    {:else}
    {#each categorie as categoria}
        <ion-col size="6">
            <ion-card class="evidenza" on:click={() => navController.push(Categoria, {categoria, comune: null, perIlTurista:true})}>
                <div class="button">
                    <div class="badge">{$t('tourist_selected_for_you')}</div>
                    <ion-img loading="lazy" src={categoria.anteprima || 'placeholder.jpg'} alt={categoria.nome}/>
                </div>
                <div class="text">
                    <ion-text class="titolo">{parseMultiLanguageContent(categoria.nome)}</ion-text>
                </div>
            </ion-card>
        </ion-col>
    {/each}
    {/if}
</ion-row>

<style>
    ion-card.evidenza {
        aspect-ratio: 1;
        height: auto;
        margin: 2px;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
    }

    ion-card.evidenza > div.button {
        flex: 0.9;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: center;
        overflow: hidden;
        position: relative;
    }

    ion-card.evidenza > div.button > ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza > div.button > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 8px 12px;
        flex: 0.3;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    ion-card.evidenza .text .titolo {
        font-size: medium;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }

    .badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: var(--ion-color-primary);
        opacity: 0.9;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        z-index: 1;
        font-weight: 500;
    }
</style>


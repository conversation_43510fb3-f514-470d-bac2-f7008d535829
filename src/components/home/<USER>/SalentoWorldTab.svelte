<script>
    import { navController } from "ionic-svelte";
    import ListaCategorieArtisti from '@pages/ListaCategorieArtisti.svelte';
    import { selectCategorieSalentoWorld, getAnteprimaCategoriaSalentoWorld } from '@lib/supabase';
    import { onMount } from 'svelte';
    import Nazione from '@pages/Nazione.svelte';
    import { t } from '@lib/i18n';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    let categorieSalentoWorld = [];
    let isLoading;

    onMount(async () => {
        isLoading = true;

        categorieSalentoWorld = await selectCategorieSalentoWorld();

        for (const categoriaSalentoWorld of categorieSalentoWorld) {
            const imageUrl = await getAnteprimaCategoriaSalentoWorld(categoriaSalentoWorld.nome);
            if (imageUrl) {
                categoriaSalentoWorld.anteprima = imageUrl;
            }
        }

        isLoading = false;
    })

    const handleNavigation = (categoria) => {
        if(categoria.tipo == 'Nazione')
            navController.push(Nazione, {categoria});
        else if(categoria.tipo == 'Artisti')
            navController.push(ListaCategorieArtisti);
    }
</script>

<!-- TITOLO -->
<ion-row>
    <ion-col>
        <ion-text class="h1">{$t('salento_world_title')}</ion-text>
    </ion-col>
</ion-row>

<ion-row>
    {#if isLoading}
        {#each Array(4) as _}
            <ion-col size="6">
                <ion-card class="evidenza skeleton">
                    <div class="button">
                        <div class="skeleton-image"></div>
                    </div>
                    <div class="text">
                        <div class="skeleton-title"></div>
                        <div class="skeleton-description"></div>
                    </div>
                </ion-card>
            </ion-col>
        {/each}
    {:else}
        {#each categorieSalentoWorld as categoria}
            <ion-col size="6">
                <ion-card class="categoria" on:click={() => handleNavigation(categoria)}>
                    <div class="button">
                        <ion-img src={categoria.anteprima || 'placeholder.jpg'} alt={categoria.nome}/>
                    </div>
                    <div class="text">
                        <ion-text class="titolo">{parseMultiLanguageContent(categoria.nome)}</ion-text>
                        <ion-text class="descrizione">{parseMultiLanguageContent(categoria.descrizione)}</ion-text>
                    </div>
                </ion-card>
            </ion-col>
        {/each}
    {/if}
</ion-row>

<style>
    ion-card.categoria {
        aspect-ratio: 1;
        height: auto;
        max-width: 170px;
        margin: 2px auto;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
        width: 100%;
    }

    ion-card.categoria > div.button {
        flex: 1;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: center;
        overflow: hidden;
    }

    ion-card.categoria > div.button > ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.categoria > div.button > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.categoria .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 016px 12px;
        gap: 2px;
        flex: 0.45;
        min-height: 85px;
    }

    ion-card.categoria .text .titolo {
        font-size: medium;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
        margin-bottom: 2px;
    }

    ion-card.categoria .text .descrizione {
        font-size: small;
        color: var(--ion-color-medium);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }

    .skeleton {
        pointer-events: none;
    }

    .skeleton-image {
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    .skeleton-title {
        height: 16px;
        width: 80%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        margin-bottom: 8px;
    }

    .skeleton-description {
        height: 12px;
        width: 90%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
</style>

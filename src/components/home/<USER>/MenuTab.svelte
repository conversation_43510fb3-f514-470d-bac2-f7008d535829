<script>
    import { CONTACT_US_MODAL_OPEN, WORK_WITH_US_MODAL_OPEN, PREFERENCES_MODAL_OPEN, PRIVACY_POLICY_MODAL_OPEN, TERMS_AND_CONDITIONS_MODAL_OPEN, ABOUT_US_MODAL_OPEN, ADD_ACTIVITY_MODAL_OPEN } from "@store";
    import Icon from '@iconify/svelte';
    import { t } from '@lib/i18n';

    const menuItems = [
      { titleKey: 'menu_preferences', descriptionKey: 'menu_preferences_desc', icon: 'ion:cog-outline', modalOpener: PREFERENCES_MODAL_OPEN },
      { titleKey: 'menu_contact_us', descriptionKey: 'menu_contact_us_desc', icon: 'ion:mail-outline', modalOpener: CONTACT_US_MODAL_OPEN },
      { titleKey: 'menu_add_activity', descriptionKey: 'menu_add_activity_desc', icon: 'ion:storefront-outline', modalOpener: ADD_ACTIVITY_MODAL_OPEN },
      { titleKey: 'menu_work_with_us', description<PERSON>ey: 'menu_work_with_us_desc', icon: 'ion:briefcase-outline', modalOpener: WORK_WITH_US_MODAL_OPEN },
      { titleKey: 'menu_about_us', descriptionKey: 'menu_about_us_desc', icon: 'ion:information-circle-outline', modalOpener: ABOUT_US_MODAL_OPEN },
      { titleKey: 'menu_privacy_policy', descriptionKey: 'menu_privacy_policy_desc', icon: 'ion:shield-half-outline', modalOpener: PRIVACY_POLICY_MODAL_OPEN },
      { titleKey: 'menu_terms', descriptionKey: 'menu_terms_desc', icon: 'ion:document-text-outline', modalOpener: TERMS_AND_CONDITIONS_MODAL_OPEN }
    ];
</script>

<!-- TITOLO -->
<ion-row class="section">
    <ion-col>
        <ion-text class="h1">{$t('tab_menu')}</ion-text>
    </ion-col>
</ion-row>

<!-- MENU ITEMS -->
<ion-row>
    <ion-col>
        <div class="menu-container">
            {#each menuItems as item}
              <ion-card class="menu-card" on:click={() => item.modalOpener?.set(true)}>
                <div class="text-container">
                  <ion-text style:font-size="large" style:color="black">{$t(item.titleKey)}</ion-text>
                  <ion-text class="description">{$t(item.descriptionKey)}</ion-text>
                </div>
                <Icon icon={item.icon} width={25} color="var(--ion-color-primary)"/>
              </ion-card>
            {/each}
          </div>
    </ion-col>
</ion-row>

<style>
  .menu-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .menu-card {
    padding: 16px;
    height: 80px;
    margin: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 10px;
  }

  .text-container {
    display: flex;
    flex-direction: column;
  }
</style>
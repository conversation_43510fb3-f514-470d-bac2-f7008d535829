<script>
    import { navController } from "ionic-svelte";
    import Icon from '@iconify/svelte';
    import { getAnteprimaCategoria, selectCategorieInEvidenza, getAnteprimaInEvidenza } from "@lib/supabase";
    import ListaComuni from "@pages/ListaComuni.svelte";
    import InEvidenza from "@pages/InEvidenza.svelte";
    import { onMount } from "svelte";
    import { t } from '@lib/i18n';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    let anteprimaComune;
    let allCategorieInEvidenza;
    let loading = true;
    $: categorieInEvidenza = [];

    onMount(async () => {
        const imageUrl = await getAnteprimaCategoria('Comune');
        anteprimaComune = imageUrl || 'placeholder.jpg';

        loading = true;
        allCategorieInEvidenza = await selectCategorieInEvidenza();
        for (const categoria of allCategorieInEvidenza) {
            const imageUrl = await getAnteprimaInEvidenza(categoria.id);
            categoria.anteprima = imageUrl || 'placeholder.jpg';
        }
        categorieInEvidenza = allCategorieInEvidenza;
        loading = false;
    })
</script>

<!-- TITOLO -->
<ion-row>
    <ion-col>
        <ion-text class="h1">{$t('tab_home')}</ion-text>
    </ion-col>
</ion-row>

<!-- COMUNI -->
<ion-row class="section">
    <ion-col>
        <ion-card class="comune" on:click={() => navController.push(ListaComuni)}>
            <ion-img src={anteprimaComune} alt="copertina"/>
            <div class="text">
                <ion-text class="nome-comune">{$t('home_see_municipalities')}</ion-text>
                <ion-text class="descrizione">{$t('home_municipalities_desc')}</ion-text>
            </div>
            <div class="icon">
                <Icon icon="ion:chevron-forward" width={30} color="var(--ion-color-primary)"/>
            </div>
        </ion-card>
    </ion-col>
</ion-row>

<!-- IN EVIDENZA -->
<ion-row>
    <ion-col>
        <ion-text class="h1 section-title">{$t('home_featured')}</ion-text>
    </ion-col>
</ion-row>

<!-- DESCRIZIONE IN EVIDENZA -->
<ion-row>
    <ion-col>
        <ion-text style:font-size="medium">{$t('home_featured_desc')}</ion-text>
    </ion-col>
</ion-row>

<!-- CATEGORIE IN EVIDENZA -->
<ion-row>
    {#if loading}
        {#each Array(4) as _}
            <ion-col size="6">
                <ion-card class="evidenza skeleton">
                    <div class="button">
                        <div class="skeleton-image"></div>
                    </div>
                    <div class="text">
                        <div class="skeleton-title"></div>
                        <div class="skeleton-description"></div>
                    </div>
                </ion-card>
            </ion-col>
        {/each}
    {:else}
        {#each categorieInEvidenza as categoriaInEvidenza}
            <ion-col size="6">
                <ion-card class="evidenza" on:click={() => navController.push(InEvidenza, {categoriaInEvidenza})}>
                    <div class="button">
                        <ion-img src={categoriaInEvidenza.anteprima || 'placeholder.jpg'} alt={categoriaInEvidenza.titolo}/>
                    </div>
                    <div class="text">
                        <ion-text class="titolo">{parseMultiLanguageContent(categoriaInEvidenza.titolo)}</ion-text>
                        <ion-text class="descrizione">{parseMultiLanguageContent(categoriaInEvidenza.descrizione)}</ion-text>
                    </div>
                </ion-card>
            </ion-col>
        {/each}
    {/if}
</ion-row>

<style>
    ion-card.comune {
        height: 120px;
        margin: 0px;
        margin-bottom: 8px;
        transition: all .5s ease-in-out;
        display: flex;
        align-items: center;
        background: white;
        border-radius: 25px;
    }

    ion-card.comune > ion-img {
        object-fit: cover;
        filter: opacity(90%);
        width: 450px;
    }

    ion-card.comune > ion-img::part(image) {
        border-radius: 25px;
    }

    ion-card.comune .icon {
        margin-right: 12px;
    }

    ion-card.comune .text {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        padding: 16px;
    }

    ion-card.comune .text .nome-comune {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--ion-color-dark);
        white-space: nowrap;
    }

    ion-card.evidenza {
        aspect-ratio: 1;
        height: auto;
        max-width: 170px;
        margin: 2px auto;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
        width: 100%;
    }

    ion-card.evidenza > div.button {
        flex: 0.55;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: center;
        overflow: hidden;
    }

    ion-card.evidenza > div.button > ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza > div.button > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 8px 12px;
        gap: 2px;
        flex: 0.45;
        min-height: 85px;
    }

    ion-card.evidenza .text .titolo {
        font-size: medium;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
        margin-bottom: 2px;
    }

    ion-card.evidenza .text .descrizione {
        font-size: smaller;
        color: var(--ion-color-medium);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }

    .skeleton {
        pointer-events: none;
    }

    .skeleton-image {
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    .skeleton-title {
        height: 16px;
        width: 80%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        margin-bottom: 8px;
    }

    .skeleton-description {
        height: 12px;
        width: 90%;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
    }
</style>
<script>
    import Icon from '@iconify/svelte';
    import { ACTIVE_TAB } from '@store';
    import { createEventDispatcher } from 'svelte';
    import { t } from '@lib/i18n';

    const dispatch = createEventDispatcher();

    // Define tabs with translation keys
    let tabs = [
        { id: 'Home', name: 'tab_home', icon: 'lucide:home'},
        { id: 'Per il turista', name: 'tab_tourist', icon: 'lucide-lab:palmtree-island-sun'},
        { id: 'Salento World', name: 'tab_salento_world', icon: 'lucide:globe-2'},
        { id: 'Menu', name: 'tab_menu', icon: 'lucide:menu'}
    ]

    const handleClick = (tab) => {
        ACTIVE_TAB.set(tab.id);
        dispatch('tabChange', {tabName: tab.id});
    }
</script>

<ion-footer>
    <div id="footer" class="tab-bar">
        {#each tabs as tab}
            <div class="tab-button" class:active={tab.id == $ACTIVE_TAB} on:click={() => handleClick(tab)}>
                <Icon icon={tab.icon} width={25} color={tab.id == $ACTIVE_TAB ? 'var(--ion-color-primary)' : 'gray'}/>
                <ion-text>{$t(tab.name)}</ion-text>
            </div>
        {/each}
    </div>
</ion-footer>

<style>
    ion-footer {
        position: fixed;
        bottom: 0;
        width: 100%;
        z-index: 1000;
        background: white;
    }

    .tab-bar {
        padding: 10px 16px;
        display: flex;
        justify-content: space-around;
        align-items: center;
        box-shadow: 0 0 5px gray;
    }

    .tab-bar {
        padding-bottom: env(safe-area-inset-bottom);
    }

    div.tab-button {
        min-width: 60px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    div.tab-button > ion-text {
        color: gray;
        font-size: small;
    }

    div.tab-button.active > ion-text {
        color: var(--ion-color-primary);
    }
</style>
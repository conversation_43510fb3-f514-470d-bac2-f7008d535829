<script>
    import { t, getAvailableLanguages, setLanguage } from '@lib/i18n';
    import { userLanguage } from '@lib/stores/preferences';
    import Icon from '@iconify/svelte';
    export let canGoBack;

    // Get available languages
    const availableLanguages = getAvailableLanguages();

    // Track current language
    let currentLanguage;
    userLanguage.subscribe(value => {
        currentLanguage = value;
    });

    // Flag dropdown state
    let isDropdownOpen = false;

    // Toggle dropdown
    function toggleDropdown() {
        isDropdownOpen = !isDropdownOpen;
    }

    // Close dropdown when clicking outside
    function closeDropdown() {
        isDropdownOpen = false;
    }

    // Change language
    function changeLanguage(langCode) {
        setLanguage(langCode);
        isDropdownOpen = false;
    }

    // Get flag icon based on language code
    function getFlagIcon(langCode) {
        switch(langCode) {
            case 'it': return 'emojione:flag-for-italy';
            case 'en': return 'emojione:flag-for-united-kingdom';
            default: return 'emojione:flag-for-italy';
        }
    }
</script>

<div class="header-container">
    <ion-toolbar class="ion-no-padding">
        <ion-grid class="ion-padding">
            <ion-row class="ion-align-items-center">
                <ion-col size={2}>
                    {#if canGoBack}
                        <ion-back-button default-href="/" text=''/>
                    {/if}
                </ion-col>

                <ion-col size={8} class="ion-text-center">
                    <img src={'logo.png'} alt={$t('app_logo')} height={50}/>
                </ion-col>

                <ion-col size={2} class="ion-text-center">
                    <div class="language-switcher">
                        <button
                            class="flag-button"
                            on:click|stopPropagation={toggleDropdown}
                        >
                            <Icon icon={getFlagIcon(currentLanguage)} width="24" height="24" />
                        </button>
                    </div>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-toolbar>

    <!-- Language dropdown portal -->
    {#if isDropdownOpen}
        <div class="language-dropdown" on:click|stopPropagation>
            {#each availableLanguages as language}
                {#if language.code !== currentLanguage}
                    <button
                        class="language-option"
                        on:click={() => changeLanguage(language.code)}
                        aria-label={language.name}
                    >
                        <Icon icon={getFlagIcon(language.code)} width="24" height="24" />
                        <span>{language.name}</span>
                    </button>
                {/if}
            {/each}
        </div>
    {/if}
</div>

<svelte:window on:click={closeDropdown} />

<style>
    .header-container {
        position: relative;
        z-index: 1000;
    }

    ion-grid {
        padding: 8px;
    }

    .language-switcher {
        position: relative;
        display: inline-block;
    }

    .flag-button {
        background: transparent;
        border: none;
        cursor: pointer;
        padding: 6px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        
    }

    .flag-button::after {
        content: '';
        display: inline-block;
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 4px solid var(--ion-color-medium);
        position: absolute;
        bottom: -2px;
        right: 0;
    }

    .flag-button:hover {
        background: rgba(0, 0, 0, 0.05);
    }

    .language-dropdown {
        position: absolute;
        top: 100%;
        right: 10px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        z-index: 9999; /* Very high z-index to ensure it's on top */
        min-width: 150px;
        overflow: hidden;

        /* Animation */
        animation: dropdown-appear 0.2s ease-out;
        transform-origin: top right;

        /* Ensure dropdown doesn't go off-screen on mobile */
        @media (max-width: 576px) {
            right: 10px;
            max-width: calc(100vw - 20px);
        }
    }

    @keyframes dropdown-appear {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    .dropdown-header {
        padding: 8px 12px;
        font-size: 14px;
        font-weight: 500;
        color: var(--ion-color-medium);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .language-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        width: 100%;
        border: none;
        background: transparent;
        cursor: pointer;
        text-align: left;
    }

    .language-option:hover {
        background: rgba(0, 0, 0, 0.05);
    }
</style>
<script>
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    export let comune;
    export let membro;
</script>

<ion-grid class="ion-no-padding">
    <!-- COPERTINA -->
    {#if membro.foto}
        <ion-row>
            <ion-col>
                <ion-img 
                    loading="lazy"
                    src={membro.foto} 
                    alt="copertina"
                />
            </ion-col>
        </ion-row>
    {/if}

    <!-- CONTENUTO -->
    <ion-row class="ion-padding">
        <ion-col>
            <ion-grid>
                <!-- NOME -->
                <ion-row class="section">
                    <ion-col size={12}>
                        <ion-text class="h1">{membro.nome} {membro.cognome}</ion-text>
                    </ion-col>
                </ion-row>

                <!-- INCARICO -->
                {#if membro.incarico}
                    <ion-row class="section">
                        <ion-col size={12}>
                            <ion-text class="h2">Incarico</ion-text>
                        </ion-col>
                        <ion-col style:padding-top="8px">
                            <ion-text style:font-size="large">{parseMultiLanguageContent(membro.incarico.replaceAll(',', ' e '))}</ion-text>
                        </ion-col>
                    </ion-row>
                {/if}

                <!-- DELEGHE -->
                {#if membro.deleghe}   
                    <ion-row class="section">
                        <ion-col size={12}>
                            <ion-text class="h2">Deleghe</ion-text>
                        </ion-col>
                        <ion-col style:padding-top="8px">
                            <ion-text style:font-size="large">{parseMultiLanguageContent(membro.deleghe)}</ion-text>
                        </ion-col>
                    </ion-row>
                {/if}

                 <!-- BIOGRAFIA -->
                {#if membro.biografia}
                    <ion-row class="section">
                        <ion-col size={12}>
                            <ion-text class="h2">Biografia e note</ion-text>
                        </ion-col>
                        <ion-col style:padding-top="8px">
                            <ion-text style:font-size="large">{parseMultiLanguageContent(membro.biografia)}</ion-text>
                        </ion-col>
                    </ion-row>
                {/if}
            </ion-grid>
        </ion-col>
    </ion-row>
</ion-grid>

<style>
</style>
<script>
    import { selectServiziInEvidenza, selectEventiInEvidenza, selectVideoInEvidenza, getAnteprimaServizio, getAnteprimaEvento, getAnteprimaVideo, getAnteprimaCategoria, selectCategorieByInEvidenzaId } from '@lib/supabase';
    import { navController } from 'ionic-svelte';
    import { EVENTO_MODAL_OPEN, EVENTO_MODAL_PROPS } from '@store';
    import { Browser } from '@capacitor/browser';
    import { toastController } from 'ionic-svelte';
    import Servi<PERSON> from '@pages/Servizio.svelte';
    import Categoria from '@pages/Categoria.svelte';
    import Icon from '@iconify/svelte';
    import { onMount } from 'svelte';
    import YoutubePlayer from '@components/youtube/YoutubePlayer.svelte';
    import { extractYoutubeVideoId } from '@lib/utils/youtube';
    import { t } from '@lib/i18n';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    export let categoriaInEvidenza;
    let servizi = [];
    let eventi = [];
    let video = [];
    let categorie = [];
    let isLoading;
    let expandedVideoId = null;

    const mesiMap = {
        1: $t('january'), 2: $t('february'), 3: $t('march'), 4: $t('april'),
        5: $t('may'), 6: $t('june'), 7: $t('july'), 8: $t('august'),
        9: $t('september'), 10: $t('october'), 11: $t('november'), 12: $t('december')
    }

    onMount(async () => {
        isLoading = true;

        // Fetch all content in parallel
        const [serviziData, eventiData, videoData, categorieData] = await Promise.all([
            selectServiziInEvidenza(categoriaInEvidenza.id),
            selectEventiInEvidenza(categoriaInEvidenza.id),
            selectVideoInEvidenza(categoriaInEvidenza.id),
            selectCategorieByInEvidenzaId(categoriaInEvidenza.id)
        ]);

        // Process servizi
        for (const servizio of serviziData || []) {
            servizio.orario = servizio.orari[0];
            servizio.indirizzo = servizio.indirizzi[0];
            servizio.anteprima = await getAnteprimaServizio(servizio.nome);
        }
        servizi = serviziData || [];

        // Process eventi
        for (const evento of eventiData || []) {
            evento.anteprima = await getAnteprimaEvento(evento.id);
        }
        eventi = eventiData || [];

        // Process video
        for (const videoItem of videoData || []) {
            videoItem.anteprima = await getAnteprimaVideo(videoItem.id);
            // Extract YouTube video ID
            videoItem.youtubeId = extractYoutubeVideoId(videoItem.link);
        }
        video = videoData || [];

        // Process categorie
        for (const categoria of categorieData || []) {
            categoria.anteprima = await getAnteprimaCategoria(categoria.nome);
        }
        categorie = categorieData || [];

        isLoading = false;
    });

    const getIndirizzo = (indirizzoServizio) => {
        return `${indirizzoServizio.via}${indirizzoServizio.numero_civico ? ', ' + indirizzoServizio.numero_civico : ''}${indirizzoServizio.cap ? ', ' + indirizzoServizio.cap : ''}${indirizzoServizio.città ? ', ' + indirizzoServizio.città : ''}`;
    }

    const openEvento = (evento) => {
        $EVENTO_MODAL_PROPS.evento = evento;
        EVENTO_MODAL_OPEN.set(true);
    }

    const openLink = async (link) => {
        try {
            if (!link.startsWith('http://') && !link.startsWith('https://')) {
                link = 'https://' + link;
            }
            await Browser.open({ url: link });
        } catch (error) {
            const toast = await toastController.create({
                message: $t('error_opening_link'),
                duration: 2000,
                position: 'bottom',
                color: 'danger'
            });
            await toast.present();
        }
    }

    const toggleVideoExpand = (videoItem) => {
        if (expandedVideoId === videoItem.id) {
            expandedVideoId = null;
        } else {
            expandedVideoId = videoItem.id;
        }
    }
</script>

<ion-grid class="ion-padding main-grid">
    <!-- TITLE -->
    <ion-row class="section">
        <ion-col>
            <ion-text class="h1">{parseMultiLanguageContent(categoriaInEvidenza.titolo)}</ion-text>
        </ion-col>
    </ion-row>

    <!-- SERVIZI IN EVIDENZA -->
    {#if servizi.length > 0}
        <ion-row>
            {#each servizi as servizio}
                <ion-col size={12} on:click={() => navController.push(Servizio, {servizio})}>
                    <ion-card class:premium={servizio.premium}>
                        <ion-img
                            loading="lazy"
                            src={servizio.anteprima || 'placeholder.jpg'}
                            alt={$t('cover_image')}
                            on:ionError={(e) => {
                                e.target.src = 'placeholder.jpg';
                            }}
                        />
                        <div id="premium-icon" class="absolute-top-right" class:ion-hide={!servizio.premium}>
                            <Icon icon="ion:star" color="var(--ion-color-warning)" width={25}/>
                        </div>
                        <ion-card-header style:padding-bottom="8px">
                            <ion-row class="ion-align-items-center ion-justify-content-between" >
                                <div style="display:flex; align-items: center; gap: 5px;">
                                    {#if servizio.tipologia}
                                        <ion-badge>{servizio.tipologia}</ion-badge>
                                    {/if}
                                    {#if servizio.indirizzo}
                                        <ion-badge color="secondary">{servizio.indirizzo.città}</ion-badge>
                                    {/if}
                                </div>
                                {#if servizio.premium}
                                    <ion-badge color="warning">{$t('premium')}</ion-badge>
                                {/if}
                            </ion-row>
                        </ion-card-header>
                        <ion-card-content>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="flex: 1;">
                                    <ion-card-title>{servizio.nome}</ion-card-title>
                                    <ion-grid class="ion-no-padding" style:color="black">
                                        <!-- INDIRIZZO -->
                                        {#if servizio.indirizzo}
                                            <ion-row class="ion-align-items-center padding-bottom-3">
                                                <ion-col size={1}>
                                                    <Icon icon="ion:location-outline" width={20}/>
                                                </ion-col>
                                                <ion-col>
                                                    <ion-text>{getIndirizzo(servizio.indirizzo)}</ion-text>
                                                </ion-col>
                                            </ion-row>
                                        {/if}

                                        <!-- TELEFONO -->
                                        <!-- {#if servizio.telefono_primario}
                                            <ion-row class="ion-align-items-center padding-bottom-3">
                                                <ion-col size={1} style:margin-right="8px">
                                                    <Icon icon="ion:call-outline" width={20}/>
                                                </ion-col>
                                                <ion-col>
                                                    <ion-text>+39 {servizio.telefono_primario}</ion-text>
                                                </ion-col>
                                            </ion-row>
                                        {/if} -->
                                    </ion-grid>
                                </div>
                                <!-- <div style="flex: 0.3;">
                                    <div class="info-button">
                                        {$t('click_for_details')}
                                    </div>
                                </div> -->
                            </div>
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            {/each}
        </ion-row>
    {/if}

    <!-- EVENTI IN EVIDENZA -->
    {#if eventi.length > 0}
        <ion-row>
            {#each eventi as evento}
                <ion-col size={12}>
                    <ion-card class="evento" on:click={() => openEvento(evento)}>
                        <div class="date-container">
                            {#if evento.anteprima}
                                <ion-img
                                    loading="lazy"
                                    src={evento.anteprima || 'placeholder.jpg'}
                                    alt={$t('cover_image')}
                                    on:ionError={(e) => {
                                        e.target.src = 'placeholder.jpg';
                                    }}
                                />
                            {/if}
                            <ion-text class="day">{new Date(evento.data).getDate()}</ion-text>
                            <ion-text class="month">{mesiMap[new Date(evento.data).getMonth() + 1]}</ion-text>
                            <ion-text class="year">{new Date(evento.data).getFullYear()}</ion-text>
                        </div>
                        <div class="text-container">
                            <ion-text class="comune">{evento.comune.toUpperCase()}</ion-text>
                            <ion-text class="titolo">{evento.titolo}</ion-text>
                            <ion-text class="luogo-e-ora">{evento.luogo} - {evento.in_corso ? $t('in_progress') : evento.ora_inizio.slice(0, -3)}</ion-text>
                        </div>
                    </ion-card>
                </ion-col>
            {/each}
        </ion-row>
    {/if}

    <!-- VIDEO IN EVIDENZA -->
    {#if video.length > 0}
        <ion-row>
            {#each video as videoItem}
                <ion-col size={12}>
                    <ion-card class="video-card" on:click={() => toggleVideoExpand(videoItem)}>
                        {#if expandedVideoId === videoItem.id && videoItem.youtubeId}
                            <div class="youtube-player-wrapper">
                                <YoutubePlayer
                                    videoId={videoItem.youtubeId}
                                    height="240px"
                                    on:error={() => expandedVideoId = null}
                                />
                            </div>
                        {:else}
                            <div class="card-image-container">
                                <ion-img
                                    loading="lazy"
                                    src={videoItem.anteprima || 'placeholder.jpg'}
                                    alt={$t('cover_image')}
                                    on:ionError={(e) => {
                                        e.target.src = 'placeholder.jpg';
                                    }}
                                />
                                <div class="youtube-icon">
                                    <Icon icon="logos:youtube-icon" width={50} />
                                </div>
                            </div>
                        {/if}
                        <ion-card-content style:padding-top="0px">
                            <h2 class="video-title">{videoItem.titolo}</h2>
                            <p class="video-description">{parseMultiLanguageContent(videoItem.descrizione)}</p>
                            {#if expandedVideoId === videoItem.id && !videoItem.youtubeId}
                                <ion-button expand="block" fill="clear" on:click|stopPropagation={() => openLink(videoItem.link)}>
                                    <ion-text>{$t('watch_on_youtube')}</ion-text>
                                </ion-button>
                            {/if}
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            {/each}
        </ion-row>
    {/if}

    <!-- CATEGORIE IN EVIDENZA -->
    {#if categorie.length > 0}
        <ion-row>
            {#each categorie as categoria}
                <ion-col size={6}>
                    <ion-card class="evidenza" on:click={() => navController.push(Categoria, {categoria})}>
                        <div class="button">
                            <ion-img
                                loading="lazy"
                                src={categoria.anteprima || 'placeholder.jpg'}
                                alt={categoria.nome}
                                on:ionError={(e) => {
                                    e.target.src = 'placeholder.jpg';
                                }}
                            />
                        </div>
                        <div class="text">
                            <ion-text class="titolo">{categoria.nome}</ion-text>
                        </div>
                    </ion-card>
                </ion-col>
            {/each}
        </ion-row>
    {/if}

    {#if servizi.length === 0 && eventi.length === 0 && video.length === 0 && categorie.length === 0}
        <ion-row>
            <ion-col size={12}>
                <ion-text style:font-size="large">{$t('no_featured_content')}</ion-text>
            </ion-col>
        </ion-row>
    {/if}
</ion-grid>

<style>
    ion-card {
        margin-top: 0px;
        margin-bottom: 8px;
        transition: all .5s ease-in-out;
    }

    ion-card.premium {
        box-shadow: 0 0 10px var(--ion-color-primary);
    }

    ion-img {
        height: 140px;
        object-fit: cover;
    }

    ion-card-title {
        font-size: x-large;
        padding-bottom: 5px;
    }

    ion-row.padding-bottom-3 {
        padding-bottom: 3px;
    }

    .absolute-top-right {
        position: absolute;
        top: 0;
        right: 0;
        margin: 10px;
    }

    :global(.md ion-label) {
        font-size: large;
    }

    /* Event card styles */
    ion-card.evento {
        height: 120px;
        margin: 0px;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        overflow: hidden;
    }

    .date-container {
        height: 100%;
        width: 120px;
        min-width: 120px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        padding: 0px 8px;
        border-radius: 25px;
        background: var(--ion-color-primary);
        overflow: hidden;
    }

    .date-container > ion-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        max-width: none;
        filter: opacity(0.4);
        z-index: 1;
    }

    .date-container > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 25px !important;
    }

    .date-container > ion-text.day {
        z-index: 2;
        font-size: 36px;
        font-weight: bold;
        position: relative;
    }

    .date-container > ion-text.month {
        z-index: 2;
        font-size: 18px;
        position: relative;
    }

    .date-container > ion-text.year {
        z-index: 2;
        position: relative;
    }

    .text-container {
        flex-grow: 1;
        height: 100%;
        padding: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 2px;
        color: black;
    }

    .text-container > ion-text.comune {
        font-size: large;
        font-weight: bold;
        color: var(--ion-color-primary);
    }

    .text-container > ion-text.titolo {
        font-size: large;
        font-weight: bold;
    }

    .text-container > ion-text.luogo-e-ora {
        font-size: medium;
        color: gray;
    }

    /* Video card styles */
    .video-card {
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .video-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    }

    .card-image-container {
        position: relative;
        height: 160px;
    }

    .youtube-player-wrapper {
        width: 100%;
        height: 240px;
        background-color: #000;
    }

    .youtube-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .video-title {
        margin: 0 0 8px 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: #000000;
        line-height: 1.4;
    }

    .video-description {
        margin: 0;
        font-size: 0.9rem;
        color: var(--ion-color-medium);
        line-height: 1.5;
    }

    ion-card.evidenza {
        aspect-ratio: 1;
        height: auto;
        margin: 2px;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
    }

    ion-card.evidenza > div.button {
        flex: 0.9;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: center;
        overflow: hidden;
        position: relative;
    }

    ion-card.evidenza > div.button > ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza > div.button > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 8px 12px;
        flex: 0.3;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    ion-card.evidenza .text .titolo {
        font-size: medium;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }
</style>
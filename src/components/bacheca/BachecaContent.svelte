<script>
    import Icon from '@iconify/svelte';
    import { toast<PERSON><PERSON>roller } from 'ionic-svelte';
    import { <PERSON><PERSON><PERSON> } from '@capacitor/browser';
    export let comune;

    const openLink = async (link) => {
        try {
            if (!link.startsWith('http://') && !link.startsWith('https://')) {
                link = 'https://' + link;
            }
            await Browser.open({ url: link });
        } catch (error) {
            const toast = await toastController.create({
                message: 'Errore nell\'apertura del link',
                duration: 2000,
                position: 'bottom',
                color: 'danger'
            });
            await toast.present();
        }
    }
</script>

<ion-grid class="ion-padding main-grid">
    <!-- TITOLO -->
    <ion-row>
        <ion-col>
            <ion-text class="h1">Bacheca</ion-text>
        </ion-col>
    </ion-row>

    <!-- PRENOTAZIONI -->
    {#if comune.link_prenotazioni}
        <ion-row>
            <ion-col>
                <ion-button expand="block" style:margin-top="8px" style:width="100%" on:click={() => openLink(comune.link_prenotazioni)}>
                    <ion-text>Prenota un appuntamento</ion-text>
                </ion-button>
            </ion-col>
        </ion-row>
    {/if}

    <!-- CARDS -->
    {#if comune.link_albo_pretorio}
        <ion-row>
            <ion-col>
                <ion-card class="albo-pretorio" on:click={() => openLink(comune.link_albo_pretorio)}>
                    <Icon icon="ion:file-tray-full" width={40} color="var(--ion-color-primary)"/>
                    <div class="text">
                        <ion-text class="nome-albo">Albo Pretorio</ion-text>
                        <ion-text class="descrizione-albo">Consulta il registro degli atti del comune</ion-text>
                    </div>
                    <div class="icon-albo">
                        <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                    </div>
                </ion-card>
            </ion-col>
        </ion-row>
    {/if}

    {#if comune.link_amministrazione_trasparente}
        <ion-row>
            <ion-col>
                <ion-card class="comune" on:click={() => openLink(comune.link_amministrazione_trasparente)}>
                    <Icon icon="ion:eye" width={40} color="var(--ion-color-primary)"/>
                    <div class="text" style="flex: 1; min-width: 0;">
                        <ion-text class="nome-comune" style="word-wrap: break-word;">Amministrazione Trasparente</ion-text>
                        <ion-text class="descrizione">Esplora la trasparenza amministrativa del comune</ion-text>
                    </div>
                    <div class="icon" style="flex-shrink: 0;">
                        <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                    </div>
                </ion-card>
            </ion-col>
        </ion-row>
    {/if}

    {#if comune.link_delibere}
        <ion-row>
            <ion-col>
                <ion-card class="comune" on:click={() => openLink(comune.link_delibere)}>
                    <Icon icon="ion:newspaper" width={40} color="var(--ion-color-primary)"/>
                    <div class="text">
                        <ion-text class="nome-comune">Delibere</ion-text>
                        <ion-text class="descrizione">Consulta le delibere della giunta comunale</ion-text>
                    </div>
                    <div class="icon">
                        <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                    </div>
                </ion-card>
            </ion-col>
        </ion-row>
    {/if}

    {#if comune.link_bandi}
        <ion-row>
            <ion-col>
                <ion-card class="comune" on:click={() => openLink(comune.link_bandi)}>
                    <Icon icon="ion:document-text" width={40} color="var(--ion-color-primary)"/>
                    <div class="text">
                        <ion-text class="nome-comune">Bandi e appalti</ion-text>
                        <ion-text class="descrizione">Scopri i bandi e gli appalti attivi</ion-text>
                    </div>
                    <div class="icon">
                        <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                    </div>
                </ion-card>
            </ion-col>
        </ion-row>
    {/if}

    {#if comune.link_anagrafe}
        <ion-row>
            <ion-col>
                <ion-card class="comune" on:click={() => openLink(comune.link_anagrafe)}>
                    <Icon icon="ion:person" width={40} color="var(--ion-color-primary)"/>
                    <div class="text">
                        <ion-text class="nome-comune">Anagrafe e Stato Civile</ion-text>
                        <ion-text class="descrizione">Vai ai servizi anagrafici e di Stato Civile del comune</ion-text>
                    </div>
                    <div class="icon">
                        <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                    </div>
                </ion-card>
            </ion-col>
        </ion-row>
    {/if}

    {#if comune.link_avvisi}
        <ion-row>
            <ion-col>
                <ion-card class="comune" on:click={() => openLink(comune.link_avvisi)}>
                    <Icon icon="ion:megaphone" width={40} color="var(--ion-color-primary)"/>
                    <div class="text">
                        <ion-text class="nome-comune">Avvisi</ion-text>
                        <ion-text class="descrizione">Consulta gli avvisi e le comunicazioni importanti</ion-text>
                    </div>
                    <div class="icon">
                        <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                    </div>
                </ion-card>
            </ion-col>
        </ion-row>
    {/if}

    {#if comune.link_catasto}
        <ion-row>
            <ion-col>
                <ion-card class="comune" on:click={() => openLink(comune.link_catasto)}>
                    <Icon icon="ion:map" width={40} color="var(--ion-color-primary)"/>
                    <div class="text">
                        <ion-text class="nome-comune">Catasto e Urbanistica</ion-text>
                        <ion-text class="descrizione">Esplora il catasto e l'urbanistica del comune</ion-text>
                    </div>
                    <div class="icon">
                        <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                    </div>
                </ion-card>
            </ion-col>
        </ion-row>
    {/if}
</ion-grid>

<style>
    ion-card.albo-pretorio {
        height: auto;
        min-height: 120px;
        margin: 0px;
        margin-bottom: 8px;
        transition: all .5s ease-in-out;
        display: flex;
        align-items: center;
        background: white;
        border-radius: 25px;
        padding: 0 16px;
    }

    ion-card.albo-pretorio .icon-albo {
        margin-right: 12px;
        flex-shrink: 0;
    }

    ion-card.albo-pretorio .text {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        padding: 16px;
        min-width: 0;
    }

    ion-card.albo-pretorio .text .nome-albo {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--ion-color-dark);
        white-space: normal;
        word-break: break-word;
        line-height: 1.2;
    }

    ion-card.albo-pretorio .text .descrizione-albo {
        font-size: 0.9rem;
        color: var(--ion-color-medium);
        white-space: normal;
        word-break: break-word;
        line-height: 1.2;
    }

    .disclaimer-card {
        background-color: var(--ion-color-light);
        padding: 1rem;
        border-radius: 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .disclaimer-title {
        color: var(--ion-color-primary);
        margin: 0px;
        font-weight: 600;
    }

    .disclaimer-text {
        font-size: medium;
        color: var(--ion-color-medium);
        margin: 0px;
    }

    .disclaimer-button {
        background-color: var(--ion-color-primary);
        color: white;
        border-radius: 4px;
        padding: 8px 16px;
        font-size: small;
        align-self: flex-end;
    }
</style>

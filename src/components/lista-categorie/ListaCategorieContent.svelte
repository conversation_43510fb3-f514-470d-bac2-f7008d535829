<script>
    import { selectCategorieByTipo, getAnteprimaCategoria } from '@lib/supabase';
    import Categoria from '@pages/Categoria.svelte';
    import { navController } from 'ionic-svelte';
    import { onMount } from 'svelte';
    
    export let tipoCategoria;
    export let comune;
    let allCategorie = [], categorie = [];
    let isLoading;

    onMount(async () => {
        isLoading = true;

        if(tipoCategoria)
            allCategorie = await selectCategorieByTipo(tipoCategoria, comune.categorie_da_escludere);

        for (const categoria of allCategorie) {
            const imageUrl = await getAnteprimaCategoria(categoria.nome);
            if (imageUrl) {
                categoria.anteprima = imageUrl;
            }
        }
        categorie = allCategorie;

        isLoading = false;
    })

    const handleSearch = (e) => {
        const query = e.target.value.trim().toLowerCase();
        if(query)
            categorie = allCategorie.filter(categoria => categoria.nome.toLowerCase().startsWith(query));
        else categorie = allCategorie;
    }

    const handleClear = () => {
        categorie = allCategorie;
    }
</script>

<ion-grid class="ion-padding main-grid">
    <ion-row>
        <ion-col>
            <ion-text class="h1">{tipoCategoria}</ion-text>
        </ion-col>
    </ion-row>
    <ion-row>
        <ion-col class="ion-no-padding">
            <ion-searchbar class="ion-padding-bottom" on:ionInput={handleSearch} on:ionClear={handleClear} placeholder="Cerca"/>
        </ion-col>
    </ion-row>

    <ion-row>
        {#if isLoading}
            {#each Array(6) as _}
                <ion-col size={6}>
                    <ion-card style="aspect-ratio: 1; margin: 2px;">
                        <ion-skeleton-text animated style="width: 100%; height: 100%;"/>
                    </ion-card>
                </ion-col>
            {/each}
        {:else}
            {#each categorie as categoria}
                <ion-col size={6} on:click={() => navController.push(Categoria, {categoria, comune})}>
                    <ion-card class="evidenza">
                        <div class="button">
                            <ion-img loading="lazy" src={categoria.anteprima || 'placeholder.jpg'} alt={categoria.nome}/>
                        </div>
                        <div class="text">
                            <ion-text class="titolo">{categoria.nome}</ion-text>
                        </div>
                    </ion-card>
                </ion-col>
            {/each}
        {/if}
    </ion-row>
</ion-grid>

<style>
    ion-card.evidenza {
        aspect-ratio: 1;
        height: auto;
        margin: 2px;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
    }

    ion-card.evidenza > div.button {
        flex: 0.9;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: center;
        overflow: hidden;
        position: relative;
    }

    ion-card.evidenza > div.button > ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza > div.button > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 8px 12px;
        flex: 0.3;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    ion-card.evidenza .text .titolo {
        font-size: medium;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }
</style>
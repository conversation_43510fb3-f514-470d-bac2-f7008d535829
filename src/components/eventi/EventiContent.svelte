<script>
    import { EVENTO_MODAL_OPEN, EVENTO_MODAL_PROPS } from '@store';
    import { selectEventiByComuneId, selectAllEventi, getAnteprimaEvento } from '@lib/supabase';
    import { onMount } from "svelte";
    import { t } from '@lib/i18n';

    export let comune;
    let allEventi= [], eventi = [];
    let isLoading;
    const mesiMap = {
        1: $t('january'),
        2: $t('february'),
        3: $t('march'),
        4: $t('april'),
        5: $t('may'),
        6: $t('june'),
        7: $t('july'),
        8: $t('august'),
        9: $t('september'),
        10: $t('october'),
        11: $t('november'),
        12: $t('december')
    }

    onMount(async () => {
        isLoading = true;

        if(comune)
            allEventi = await selectEventiByComuneId(comune.id) || [];
        else
            allEventi = await selectAllEventi() || [];

        for (const evento of allEventi) {
            const imageUrl = await getAnteprimaEvento(evento.id);
            if (imageUrl) {
                evento.anteprima = imageUrl;
            }
        }

        eventi = allEventi;
        isLoading = false;
    })

    const openEvento = (evento) => {
        $EVENTO_MODAL_PROPS.evento = evento;
        $EVENTO_MODAL_PROPS.comune = comune;
        EVENTO_MODAL_OPEN.set(true);
    }
</script>


<ion-grid class="ion-no-padding" class:ion-padding={comune}>
    <!-- TITOLO -->
    <ion-row class="section">
        <ion-col size={12}>
            <ion-text class="h1">{$t('events')}</ion-text>
        </ion-col>
    </ion-row>

    <!-- DESCRIZIONE -->
    {#if comune}
        <ion-row class="section">
            <ion-col>
                <ion-text style:font-size="large">{$t('all_events_for')} {comune.nome.toUpperCase()}</ion-text>
            </ion-col>
        </ion-row>
    {:else}
        <ion-row class="section">
            <ion-col>
                <ion-text style:font-size="large">{$t('events_for_all_municipalities')}</ion-text>
            </ion-col>
        </ion-row>
    {/if}

    <!-- CARDS EVENTI -->
    <ion-row>
        <ion-col>
            {#each eventi as evento}
                <ion-card class="evento" on:click={() => openEvento(evento)}>
                    <div class="date-container">
                        {#if evento.anteprima}
                            <ion-img
                                loading="lazy"
                                src={evento.anteprima || 'placeholder.jpg'}
                                alt={$t('cover_image')}
                                on:ionError={(e) => {
                                    e.target.src = 'placeholder.jpg';
                                }}
                            />
                        {/if}
                        <ion-text class="day">{new Date(evento.data).getDate()}</ion-text>
                        <ion-text class="month">{mesiMap[new Date(evento.data).getMonth() + 1]}</ion-text>
                        <ion-text class="year">{new Date(evento.data).getFullYear()}</ion-text>
                    </div>
                    <div class="text-container">
                        {#if comune}
                            <ion-text class="comune">{comune.nome.toUpperCase()}</ion-text>
                        {:else}
                            <ion-text class="comune">{evento.comune.toUpperCase()}</ion-text>
                        {/if}
                        <ion-text class="titolo">{evento.titolo}</ion-text>
                        <ion-text class="luogo-e-ora">{evento.luogo} - {evento.in_corso ? $t('in_progress') : evento.ora_inizio.slice(0, -3)}</ion-text>
                    </div>
                </ion-card>
            {:else}
                <ion-text style:font-size="large">{$t('no_events_scheduled')}</ion-text>
            {/each}
        </ion-col>
    </ion-row>
</ion-grid>

<style>
    ion-card.evento {
        height: 120px;
        margin: 0px;
        margin-bottom: 16px;
        transition: all .5s ease-in-out;
        display: flex;
        align-items: center;
        overflow: hidden;
    }

    ion-card.evento > div.date-container {
        height: 100%;
        width: 120px;
        min-width: 120px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        padding: 0px 8px;
        border-radius: 25px;
        background: var(--ion-color-primary);
        overflow: hidden;
    }

    ion-card.evento > div.date-container > ion-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        max-width: none;
        filter: opacity(0.4);
        z-index: 1;
    }

    ion-card.evento > div.date-container > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 25px !important;
    }

    ion-card.evento > div.date-container > ion-text.day {
        z-index: 2;
        font-size: 36;
        font-weight: bold;
        position: relative;
    }

    ion-card.evento > div.date-container > ion-text.month {
        z-index: 2;
        font-size: 18;
        position: relative;
    }

    ion-card.evento > div.date-container > ion-text.year {
        z-index: 2;
        position: relative;
    }

    ion-card.evento > div.text-container {
        flex-grow: 1;
        height: 100%;
        padding: 16px;

        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 2px;
        color: black;
    }

    ion-card.evento > div.text-container > ion-text.comune {
        font-size: large;
        font-weight: bold;
        color: var(--ion-color-primary);
    }

    ion-card.evento > div.text-container > ion-text.titolo {
        font-size: large;
        font-weight: bold;
    }

    ion-card.evento > div.text-container > ion-text.luogo-e-ora {
        font-size: medium;
        color: gray;
    }
</style>
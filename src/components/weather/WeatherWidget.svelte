<script>
    import { onMount } from "svelte";
    import Icon from '@iconify/svelte';

    export let location;
    export let apiKey; // This should be passed from the parent component or stored in environment variables

    let weatherData = null;
    let isLoading = true;
    let error = null;

    onMount(async () => {
        if (!location) {
            error = "Location is required";
            isLoading = false;
            return;
        }

        if (!apiKey) {
            error = "API key is missing";
            isLoading = false;
            return;
        }

        try {
            // Add Italy to the location to improve accuracy
            const searchLocation = `${location}, Italy`;
            const response = await fetch(`https://api.weatherapi.com/v1/current.json?key=${apiKey}&q=${encodeURIComponent(searchLocation)}&aqi=no`);

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error?.message || 'Failed to fetch weather data');
            }

            weatherData = await response.json();
            isLoading = false;
        } catch (err) {
            console.error('Weather API error:', err);
            error = err.message || 'Failed to fetch weather data';
            isLoading = false;
        }
    });
</script>

<div class="weather-widget">
    {#if isLoading}
        <div class="weather-loading">
            <ion-spinner name="crescent" color="primary"></ion-spinner>
        </div>
    {:else if error}
        <div class="weather-error" title={error}>
            <Icon icon="ion:alert-circle-outline" width={20} color="var(--ion-color-danger)" />
        </div>
    {:else if weatherData}
        <div class="weather-content" title={weatherData.current.condition.text}>
            <img
                src={weatherData.current.condition.icon}
                alt={weatherData.current.condition.text}
                class="weather-icon"
            />
            <div class="weather-temp">
                {Math.round(weatherData.current.temp_c)}°C
            </div>
        </div>
    {/if}
</div>

<style>
    .weather-widget {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 80px;
        height: 40px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 0 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .weather-loading, .weather-error {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        cursor: help;
    }

    .weather-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        cursor: help;
    }

    .weather-icon {
        width: 40px;
        height: 40px;
    }

    .weather-temp {
        font-size: 1rem;
        font-weight: 600;
        color: var(--ion-color-dark);
    }
</style>

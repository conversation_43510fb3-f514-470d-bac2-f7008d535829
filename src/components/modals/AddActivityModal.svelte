<script>
    import { ADD_ACTIVITY_MODAL_OPEN } from '@store';
    import { insertRichiestaInserimentoAttivita } from '@lib/supabase/database';
    import { uploadFotoRichiestaInserimentoAttivita } from '@lib/supabase/storage';
    import Icon from '@iconify/svelte';
    import { toastController } from 'ionic-svelte';
    import { t } from '@lib/i18n';

    // Initialize all variables with empty strings
    let responsabile_nome = '',
        responsabile_cognome = '',
        nome_azienda = '',
        ragione_sociale = '',
        via = '',
        paese = '',
        cap = '',
        telefono = '',
        email = '',
        piva_cf = '',
        codice_univoco = '',
        note = '',
        paese_visibilita = '',
        categoria = '',
        video_link = '',
        indirizzo_visibilita = '',
        telefono_visibilita = '',
        email_visibilita = '',
        sito_web = '',
        codice_intermediario = '',
        fotoFile = null;

    let breakpoints = [0, 1];

    const handleInput = (e, field) => {
        const value = e.target.value;
        switch(field) {
            case 'responsabile_nome':
                responsabile_nome = value;
                break;
            case 'responsabile_cognome':
                responsabile_cognome = value;
                break;
            case 'nome_azienda':
                nome_azienda = value;
                break;
            case 'ragione_sociale':
                ragione_sociale = value;
                break;
            case 'via':
                via = value;
                break;
            case 'paese':
                paese = value;
                break;
            case 'cap':
                cap = value;
                break;
            case 'telefono':
                telefono = value;
                break;
            case 'email':
                email = value;
                break;
            case 'piva_cf':
                piva_cf = value;
                break;
            case 'codice_univoco':
                codice_univoco = value;
                break;
            case 'note':
                note = value;
                break;
            case 'paese_visibilita':
                paese_visibilita = value;
                break;
            case 'categoria':
                categoria = value;
                break;
            case 'video_link':
                video_link = value;
                break;
            case 'indirizzo_visibilita':
                indirizzo_visibilita = value;
                break;
            case 'telefono_visibilita':
                telefono_visibilita = value;
                break;
            case 'email_visibilita':
                email_visibilita = value;
                break;
            case 'sito_web':
                sito_web = value;
                break;
            case 'codice_intermediario':
                codice_intermediario = value;
                break;
        }
    };

    const handleFileInput = (e) => {
        console.log('Input event:', e); // For debugging

        // Get the actual input element
        const inputElement = e.target.querySelector('input') || e.target;

        if (inputElement && inputElement.files && inputElement.files.length > 0) {
            fotoFile = inputElement.files[0];
            console.log('File selected:', fotoFile); // For debugging
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            let fotoFileName = null;

            // First upload the photo if one was selected
            if (fotoFile) {
                fotoFileName = await uploadFotoRichiestaInserimentoAttivita(
                    fotoFile,
                    nome_azienda.replace(/\s+/g, '-').toLowerCase()
                );
            }

            // Then, insert into database
            const datiRichiesta = {
                responsabile_nome,
                responsabile_cognome,
                nome_azienda,
                ragione_sociale,
                via,
                paese,
                cap,
                telefono,
                email,
                piva_cf,
                codice_univoco,
                note,
                paese_visibilita,
                categoria,
                video_link,
                indirizzo_visibilita,
                telefono_visibilita,
                email_visibilita,
                sito_web,
                codice_intermediario
            };

            await insertRichiestaInserimentoAttivita(datiRichiesta);

            // Then, send email
            const emailBody = `
                DATI RESPONSABILE:
                Nome: ${responsabile_nome}
                Cognome: ${responsabile_cognome}
                Nome Azienda: ${nome_azienda}
                Ragione Sociale: ${ragione_sociale}
                Via: ${via}
                Paese: ${paese}
                CAP: ${cap}
                Telefono: ${telefono}
                Email: ${email}
                P.IVA/CF: ${piva_cf}
                Codice Univoco: ${codice_univoco}
                Note: ${note || 'Nessuna nota'}

                DATI VISIBILITÀ:
                Paese: ${paese_visibilita}
                Categoria: ${categoria}
                Video Link: ${video_link || 'Non fornito'}
                Indirizzo: ${indirizzo_visibilita}
                Telefono: ${telefono_visibilita}
                Email: ${email_visibilita}
                Sito Web: ${sito_web || 'Non fornito'}

                Codice Intermediario: ${codice_intermediario || 'Non fornito'}`;

            const mailtoLink = `mailto:<EMAIL>?subject=Nuova richiesta di inserimento attività&body=${encodeURIComponent(emailBody)}`;
            window.location.href = mailtoLink;

            // Close the modal after successful submission
            handleDismiss();

        } catch (error) {
            console.error('Form submission error:', error);
            const toast = await toastController.create({
                message: `${$t('error_generic')}: ${error.message || error}`,
                duration: 2000,
                position: 'bottom',
                color: 'danger'
            });
            await toast.present();
        }
    };

    const handleDismiss = () => {
        // Reset all fields to empty strings instead of null
        responsabile_nome = '';
        responsabile_cognome = '';
        nome_azienda = '';
        ragione_sociale = '';
        via = '';
        paese = '';
        cap = '';
        telefono = '';
        email = '';
        piva_cf = '';
        codice_univoco = '';
        note = '';
        paese_visibilita = '';
        categoria = '';
        video_link = '';
        indirizzo_visibilita = '';
        telefono_visibilita = '';
        email_visibilita = '';
        sito_web = '';
        codice_intermediario = '';
        fotoFile = null; // Reset the file
        ADD_ACTIVITY_MODAL_OPEN.set(false);
    };

    const validateEmail = (e) => {
        const input = e.target;
        const value = e.target.value;

        input.classList.remove('ion-touched');
        input.classList.remove('ion-valid');
        input.classList.remove('ion-invalid');

        const isValidEmail = value.match(/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/)
        isValidEmail ? input.classList.add('ion-valid') : input.classList.add('ion-invalid');

        if(value) input.classList.add('ion-touched');
    }
</script>

<ion-modal
    mode="md"
    is-open={$ADD_ACTIVITY_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={1}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            <!-- TITLE -->
            <ion-row class="section">
                <ion-col size={12} class="ion-text-center">
                    <ion-text class="h1">{$t('add_activity_title')}</ion-text>
                    <p class="subtitle">{$t('add_activity_subtitle')}</p>
                </ion-col>
            </ion-row>

            <!-- FORM -->
            <form class="contact-form" on:submit={handleSubmit}>
                <ion-row>
                    <!-- IL RESPONSABILE -->
                    <ion-col size={12}>
                        <ion-text class="section-title">{$t('add_activity_manager_section')}</ion-text>
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Mario"
                            label={$t('add_activity_manager_name') + '*'}
                            label-placement="stacked"
                            name="responsabile_nome"
                            type="text"
                            value={responsabile_nome}
                            on:ionInput={(e) => handleInput(e, 'responsabile_nome')}
                        />
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Rossi"
                            label={$t('add_activity_manager_surname') + '*'}
                            label-placement="stacked"
                            name="responsabile_cognome"
                            type="text"
                            value={responsabile_cognome}
                            on:ionInput={(e) => handleInput(e, 'responsabile_cognome')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Nome Azienda"
                            label={$t('add_activity_company_name') + '*'}
                            label-placement="stacked"
                            name="nome_azienda"
                            type="text"
                            value={nome_azienda}
                            on:ionInput={(e) => handleInput(e, 'nome_azienda')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Ragione Sociale"
                            label={$t('add_activity_company_type') + '*'}
                            label-placement="stacked"
                            name="ragione_sociale"
                            type="text"
                            value={ragione_sociale}
                            on:ionInput={(e) => handleInput(e, 'ragione_sociale')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Via"
                            label={$t('add_activity_address') + '*'}
                            label-placement="stacked"
                            name="via"
                            type="text"
                            value={via}
                            on:ionInput={(e) => handleInput(e, 'via')}
                        />
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Paese"
                            label={$t('add_activity_city') + '*'}
                            label-placement="stacked"
                            name="paese"
                            type="text"
                            value={paese}
                            on:ionInput={(e) => handleInput(e, 'paese')}
                        />
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="CAP"
                            label={$t('add_activity_zip') + '*'}
                            label-placement="stacked"
                            name="cap"
                            type="text"
                            value={cap}
                            on:ionInput={(e) => handleInput(e, 'cap')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Telefono/Cellulare"
                            label={$t('add_activity_phone') + '*'}
                            label-placement="stacked"
                            name="telefono"
                            type="tel"
                            value={telefono}
                            on:ionInput={(e) => handleInput(e, 'telefono')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Email o PEC"
                            label={$t('add_activity_email') + '*'}
                            label-placement="stacked"
                            error-text={$t('error_invalid_email')}
                            name="email"
                            type="email"
                            value={email}
                            on:ionInput={(e) => {
                                handleInput(e, 'email');
                                validateEmail(e);
                            }}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="P.IVA o Codice Fiscale"
                            label={$t('add_activity_vat') + '*'}
                            label-placement="stacked"
                            name="piva_cf"
                            type="text"
                            value={piva_cf}
                            on:ionInput={(e) => handleInput(e, 'piva_cf')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Codice Univoco"
                            label={$t('add_activity_unique_code') + '*'}
                            label-placement="stacked"
                            name="codice_univoco"
                            type="text"
                            value={codice_univoco}
                            on:ionInput={(e) => handleInput(e, 'codice_univoco')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            fill="outline"
                            label={$t('add_activity_notes')}
                            label-placement="stacked"
                            placeholder="Note aggiuntive"
                            name="note"
                            value={note}
                            on:ionInput={(e) => handleInput(e, 'note')}
                        />
                    </ion-col>

                    <!-- DATI VISIBILITÀ -->
                    <ion-col size={12}>
                        <ion-text class="section-title">{$t('add_activity_visibility_section')}</ion-text>
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Paese"
                            label={$t('add_activity_visibility_city') + '*'}
                            label-placement="stacked"
                            name="paese_visibilita"
                            type="text"
                            value={paese_visibilita}
                            on:ionInput={(e) => handleInput(e, 'paese_visibilita')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Categoria"
                            label={$t('add_activity_category') + '*'}
                            label-placement="stacked"
                            name="categoria"
                            type="text"
                            value={categoria}
                            on:ionInput={(e) => handleInput(e, 'categoria')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            label={$t('add_activity_photo') + '*'}
                            label-placement="stacked"
                            name="foto"
                            type="file"
                            accept="image/*"
                            on:ionChange={handleFileInput}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            fill="outline"
                            placeholder="Link al video"
                            label={$t('add_activity_video_link')}
                            label-placement="stacked"
                            name="video_link"
                            type="url"
                            value={video_link}
                            on:ionInput={(e) => handleInput(e, 'video_link')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Indirizzo"
                            label={$t('add_activity_visibility_address') + '*'}
                            label-placement="stacked"
                            name="indirizzo_visibilita"
                            type="text"
                            value={indirizzo_visibilita}
                            on:ionInput={(e) => handleInput(e, 'indirizzo_visibilita')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Telefono"
                            label={$t('add_activity_visibility_phone') + '*'}
                            label-placement="stacked"
                            name="telefono_visibilita"
                            type="tel"
                            value={telefono_visibilita}
                            on:ionInput={(e) => handleInput(e, 'telefono_visibilita')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Email"
                            label={$t('add_activity_visibility_email') + '*'}
                            label-placement="stacked"
                            name="email_visibilita"
                            type="email"
                            value={email_visibilita}
                            on:ionInput={(e) => handleInput(e, 'email_visibilita')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            fill="outline"
                            placeholder="Sito web"
                            label={$t('add_activity_website')}
                            label-placement="stacked"
                            name="sito_web"
                            type="url"
                            value={sito_web}
                            on:ionInput={(e) => handleInput(e, 'sito_web')}
                        />
                    </ion-col>

                    <!-- RISERVATO AL PERSONALE -->
                    <ion-col size={12}>
                        <ion-text class="section-title">{$t('add_activity_reserved_section')}</ion-text>
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            fill="outline"
                            placeholder="Codice intermediario"
                            label={$t('add_activity_intermediary_code')}
                            label-placement="stacked"
                            name="codice_intermediario"
                            type="text"
                            value={codice_intermediario}
                            on:ionInput={(e) => handleInput(e, 'codice_intermediario')}
                        />
                    </ion-col>

                    <ion-col size={12}>
                        <ion-button expand="block" type="submit">
                            {$t('add_activity_send')}
                            <Icon
                                icon="ion:paper-plane"
                                slot="end"
                                width="20"
                                height="20"
                                style="margin-left: 8px"
                            />
                        </ion-button>
                    </ion-col>
                </ion-row>
            </form>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    .subtitle {
        margin-top: 8px;
        color: var(--ion-color-medium);
        font-size: large;
        line-height: 1.4;
    }

    .section-title {
        display: block;
        font-size: 1.2em;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 16px 0 8px 0;
    }

    .contact-form {
        width: 100%;
    }

    .contact-form ion-button {
        margin-top: 8px;
        --border-radius: 12px;
        --box-shadow: none;
        height: 48px;
        font-weight: 600;
    }

    .contact-form ion-button::part(native) {
        padding-right: 16px;
    }
</style>
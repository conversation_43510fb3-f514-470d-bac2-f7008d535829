<script>
    import { CONTACT_US_MODAL_OPEN } from '@store';
    import { insertRichiestaContatto } from '@lib/supabase/database';
    import Icon from '@iconify/svelte';
    import { toastController } from 'ionic-svelte';
    import { t } from '@lib/i18n';

    let nome = '', cognome = '', email = '', messaggio = '';
    let breakpoints = [0, 0.7, 1];

    const handleInput = (e, field) => {
        const value = e.target.value;
        switch(field) {
            case 'nome':
                nome = value;
                break;
            case 'cognome':
                cognome = value;
                break;
            case 'email':
                email = value;
                break;
            case 'messaggio':
                messaggio = value;
                break;
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        try {
            // First, insert into database
            const datiRichiesta = {
                nome,
                cognome,
                email,
                messaggio
            };

            try {
                await insertRichiestaContatto(datiRichiesta);
            } catch (dbError) {
                console.error('Database error:', dbError);
                throw dbError;
            }

            // Then, send email
            const emailBody = `
                DATI CONTATTO:
                ${$t('contact_name')}: ${nome}
                ${$t('contact_surname')}: ${cognome}
                ${$t('contact_email')}: ${email}
                ${$t('contact_message')}: ${messaggio}`;

            const mailtoLink = `mailto:<EMAIL>?subject=Nuovo messaggio di contatto&body=${encodeURIComponent(emailBody)}`;
            window.location.href = mailtoLink;

            // Close the modal after successful submission
            handleDismiss();

            // Show success toast
            const toast = await toastController.create({
                message: $t('contact_success'),
                duration: 2000,
                position: 'bottom',
                color: 'success'
            });
            await toast.present();

        } catch (error) {
            console.error('Form submission error:', error);
            const toast = await toastController.create({
                message: `${$t('contact_error')}: ${error.message || error}`,
                duration: 2000,
                position: 'bottom',
                color: 'danger'
            });
            await toast.present();
        }
    };

    const handleDismiss = () => {
        nome = null;
        cognome = null;
        email = null;
        messaggio = null;
        CONTACT_US_MODAL_OPEN.set(false);
    };

    const validateEmail = (e) => {
        const input = e.target;
        const value = e.target.value;

        input.classList.remove('ion-touched');
        input.classList.remove('ion-valid');
        input.classList.remove('ion-invalid');

        const isValidEmail = value.match(/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/)
        isValidEmail ? input.classList.add('ion-valid') : input.classList.add('ion-invalid');

        if(value) input.classList.add('ion-touched');
    }
</script>

<ion-modal
    mode="md"
    is-open={$CONTACT_US_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={0.7}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            <!-- TITLE -->
            <ion-row class="section">
                <ion-col size={12} class="ion-text-center">
                    <ion-text class="h1">{$t('contact_title')}</ion-text>
                    <p class="subtitle">{$t('contact_subtitle')}</p>
                </ion-col>
            </ion-row>

            <!-- CONTACT FORM -->
            <form class="contact-form" on:submit={handleSubmit}>
                <ion-row>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Mario"
                            label={$t('contact_name') + '*'}
                            label-placement="stacked"
                            name="nome"
                            type="text"
                            value={nome}
                            on:ionInput={(e) => handleInput(e, 'nome')}
                        />
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Rossi"
                            label={$t('contact_surname') + '*'}
                            label-placement="stacked"
                            name="cognome"
                            type="text"
                            value={cognome}
                            on:ionInput={(e) => handleInput(e, 'cognome')}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="<EMAIL>"
                            label={$t('contact_email') + '*'}
                            label-placement="stacked"
                            error-text={$t('error_invalid_email')}
                            name="email"
                            type="email"
                            value={email}
                            on:ionInput={(e) => {
                                handleInput(e, 'email');
                                validateEmail(e);
                            }}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            required
                            fill="outline"
                            label={$t('contact_message') + '*'}
                            label-placement="stacked"
                            placeholder={$t('contact_message')}
                            name="messaggio"
                            value={messaggio}
                            on:ionInput={(e) => handleInput(e, 'messaggio')}
                            style:min-height="150px"
                        />
                    </ion-col>
                    <ion-col size={12}>
                        <ion-button expand="block" type="submit">
                            {$t('contact_send')}
                            <Icon
                                icon="ion:paper-plane"
                                slot="end"
                                width="20"
                                height="20"
                                style="margin-left: 8px"
                            />
                        </ion-button>
                    </ion-col>
                </ion-row>
            </form>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    .subtitle {
        margin-top: 8px;
        color: var(--ion-color-medium);
        font-size: large;
        line-height: 1.4;
    }

    .contact-form {
        width: 100%;
    }

    .contact-form ion-button {
        margin-top: 8px;
        --border-radius: 12px;
        --box-shadow: none;
        height: 48px;
        font-weight: 600;
    }

    .contact-form ion-button::part(native) {
        padding-right: 16px;
    }
</style>

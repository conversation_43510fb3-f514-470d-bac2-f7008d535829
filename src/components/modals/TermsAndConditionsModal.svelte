<script>
    import { TERMS_AND_CONDITIONS_MODAL_OPEN } from '@store';
    import { t } from '@lib/i18n';

    let breakpoints = [0, 0.7, 1];

    const handleDismiss = () => {
        TERMS_AND_CONDITIONS_MODAL_OPEN.set(false);
    };
</script>

<ion-modal
    mode="md"
    is-open={$TERMS_AND_CONDITIONS_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={0.7}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            <!-- TITLE -->
            <ion-row class="section">
                <ion-col size={12} class="ion-text-center">
                    <ion-text class="h1">{$t('terms_title')}</ion-text>
                    <p class="subtitle">{$t('terms_subtitle')}</p>
                </ion-col>
            </ion-row>

            <!-- CONTENT -->
            <ion-row>
                <ion-col>
                    <div class="terms-content">
                        <ion-text class="h2">{$t('terms_section1_title')}</ion-text>
                        <p>{$t('terms_section1_content')}</p>

                        <ion-text class="h2">{$t('terms_section2_title')}</ion-text>
                        <p>{$t('terms_section2_content')}</p>
                        <ul>
                            <li>{$t('terms_section2_item1')}</li>
                            <li>{$t('terms_section2_item2')}</li>
                            <li>{$t('terms_section2_item3')}</li>
                            <li>{$t('terms_section2_item4')}</li>
                            <li>{$t('terms_section2_item5')}</li>
                        </ul>

                        <ion-text class="h2">{$t('terms_section3_title')}</ion-text>
                        <p>{$t('terms_section3_content')}</p>
                        <ul>
                            <li>{$t('terms_section3_item1')}</li>
                        </ul>
                        <p>{$t('terms_section3_item2')}</p>

                        <ion-text class="h2">{$t('terms_section4_title')}</ion-text>
                        <p>{$t('terms_section4_content')}</p>
                        <ul>
                            <li>{$t('terms_section4_item1')}</li>
                            <li>{$t('terms_section4_item2')}</li>
                        </ul>
                        <p>{$t('terms_section4_item3')}</p>

                        <ion-text class="h2">{$t('terms_section5_title')}</ion-text>
                        <p>{$t('terms_section5_content')}</p>
                        <ul>
                            <li>{$t('terms_section5_item1')}</li>
                            <li>{$t('terms_section5_item2')}</li>
                            <li>{$t('terms_section5_item3')}</li>
                            <li>{$t('terms_section5_item4')}</li>
                        </ul>

                        <ion-text class="h2">{$t('terms_section6_title')}</ion-text>
                        <p>{$t('terms_section6_content')}</p>

                        <ion-text class="h2">{$t('terms_section7_title')}</ion-text>
                        <p>{$t('terms_section7_content')}</p>

                        <ion-text class="h2">{$t('terms_section8_title')}</ion-text>
                        <p>{$t('terms_section8_content')}</p>

                        <ion-text class="h2">{$t('terms_section9_title')}</ion-text>
                        <p>{$t('terms_section9_content')}</p>
                        <p>Email: <EMAIL></p>
                        <p>Indirizzo: Viale Savoia 12, 73025 Martano (LE)</p>
                    </div>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    .subtitle {
        margin-top: 8px;
        margin-bottom: 8px;
        color: var(--ion-color-medium);
        font-size: large;
        line-height: 1.4;
    }
</style>

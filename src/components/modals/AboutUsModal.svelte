<script>
    import { ABOUT_US_MODAL_OPEN } from '@store';
    import Icon from '@iconify/svelte';
    import { t } from '@lib/i18n';

    let breakpoints = [0, 0.8, 1];

    const handleDismiss = () => {
        ABOUT_US_MODAL_OPEN.set(false);
    };
</script>

<ion-modal
    mode="md"
    is-open={$ABOUT_US_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={0.8}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            <!-- TITLE -->
            <ion-row class="section">
                <ion-col size={12} class="ion-text-center">
                    <ion-text class="h1">{$t('about_us_title')}</ion-text>
                    <p class="subtitle">{$t('about_us_subtitle')}</p>
                </ion-col>
            </ion-row>

            <!-- CONTENT -->
            <ion-row>
                <ion-col>
                    <div class="about-content">
                        <p>
                            {$t('about_us_welcome')}
                        </p>
                        <p>
                            {$t('about_us_team')}
                        </p>
                        <ul>
                            <li>
                                <Icon icon="mdi:cellphone-android" width={24} color="var(--ion-color-medium)"/>
                                {$t('about_us_app_dev')}
                            </li>
                            <li>
                                <Icon icon="mdi:web" width={24} color="var(--ion-color-medium)"/>
                                {$t('about_us_web_dev')}
                            </li>
                            <li>
                                <Icon icon="mdi:video" width={24} color="var(--ion-color-medium)"/>
                                {$t('about_us_video_prod')}
                            </li>
                            <li>
                                <Icon icon="mdi:music" width={24} color="var(--ion-color-medium)"/>
                                {$t('about_us_music_prod')}
                            </li>
                        </ul>
                        <p>
                            {$t('about_us_mission')}
                            <br>{$t('about_us_discover')}
                        </p>
                        <p>{$t('about_us_contact')} <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    .about-content {
        text-align: left;
        color: var(--ion-color-medium);
        font-size: large;
        line-height: 1.4;
    }

    .about-content ul {
        padding-left: 0;
        margin-top: 8px;
        list-style: none;
    }

    .about-content li {
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .subtitle {
        color: var(--ion-color-medium);
        margin-top: 4px;
    }
</style>
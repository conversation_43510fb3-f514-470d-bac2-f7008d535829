<script>
    import { EVENTO_MODAL_OPEN, EVENTO_MODAL_PROPS} from '@store';
    import { t } from '@lib/i18n';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    let breakpoints = [0, 1];
    let evento = {}, comune = {};

    const mesiMap = {
        1: $t('january'),
        2: $t('february'),
        3: $t('march'),
        4: $t('april'),
        5: $t('may'),
        6: $t('june'),
        7: $t('july'),
        8: $t('august'),
        9: $t('september'),
        10: $t('october'),
        11: $t('november'),
        12: $t('december')
    }

    const handleOpen = () => {
        evento = $EVENTO_MODAL_PROPS?.evento;
        comune = evento.comune ? {nome: evento.comune} : $EVENTO_MODAL_PROPS?.comune;
    }

    const handleDismiss = () => {
        EVENTO_MODAL_PROPS.set({});
        EVENTO_MODAL_OPEN.set(false);
    };
</script>

<ion-modal
is-open={$EVENTO_MODAL_OPEN}
initial-breakpoint="1"
{breakpoints}
on:ionModalDidDismiss={handleDismiss}
on:ionModalWillPresent={handleOpen}>
    <ion-content>
        <ion-grid class="ion-no-padding">
            <!-- COPERTINA -->
            <ion-row>
                <ion-col>
                    {#if evento.anteprima}
                        <ion-img src={evento.anteprima} alt={$t('cover_image')}/>
                    {/if}
                </ion-col>
            </ion-row>

            <!-- CONTENUTO -->
            <ion-row class="ion-padding">
                <ion-col>
                    <ion-grid>
                        <!-- TITOLO -->
                        <ion-row class="section">
                            <ion-col size={12}>
                                <ion-text class="h1">{evento.titolo}</ion-text>
                            </ion-col>
                            <ion-col style:padding-top="8px">
                                <ion-text style:font-size="large">{evento.descrizione ? parseMultiLanguageContent(evento.descrizione) : $t('no_description_available')}</ion-text>
                            </ion-col>
                        </ion-row>

                        <!-- LUOGO E ORA -->
                        <ion-row class="section">
                            <ion-col size={12}>
                                <ion-text class="h2">{$t('time_and_place')}</ion-text>
                            </ion-col>
                            <ion-col style:padding-top="8px">
                                <ion-text style:font-size="large">{new Date(evento.data).getDate()} {mesiMap[new Date(evento.data).getMonth() + 1]} {$t('from')} {evento.ora_inizio?.slice(0, -3)} {$t('to')} {evento.ora_fine?.slice(0, -3)} {$t('at')} {evento.luogo} {$t('of')} {comune.nome}</ion-text>
                            </ion-col>
                        </ion-row>

                    </ion-grid>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    ion-modal {
        --backdrop-opacity: 0.8;
        backdrop-filter: blur(1px);

        &::part(content) {
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            box-shadow: 0px 0px 20px 5px rgb(0 0 0 / 32%);
        }
    }

    ion-img {
        height: 256px;
        object-fit: cover;
    }
</style>
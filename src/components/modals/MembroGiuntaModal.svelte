<script>
    import { MEMBRO_GIUNTA_MODAL_OPEN, MEMBRO_GIUNTA_MODAL_PROPS} from '@store';
    import Icon from '@iconify/svelte';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    let breakpoints = [0, 0.9, 1];
    let membro = {}, comune = {};

    const handleOpen = () => {
        membro = $MEMBRO_GIUNTA_MODAL_PROPS?.membro || {};
        comune = $MEMBRO_GIUNTA_MODAL_PROPS?.comune || {};
    }

    const handleDismiss = () => {
        MEMBRO_GIUNTA_MODAL_PROPS.set({});
        MEMBRO_GIUNTA_MODAL_OPEN.set(false);
    };
</script>

<ion-modal
is-open={$MEMBRO_GIUNTA_MODAL_OPEN}
initial-breakpoint="0.9"
{breakpoints}
on:ionModalDidDismiss={handleDismiss}
on:ionModalWillPresent={handleOpen}>
    <ion-content>
        <ion-grid class="ion-no-padding">
            <!-- COPERTINA -->
            <ion-row>
                <ion-col>
                    <ion-img 
                        loading="lazy"
                        src={membro.foto || 'placeholder.jpg'} 
                        alt="copertina"
                        on:ionError={(e) => {
                            e.target.src = 'placeholder.jpg';
                        }}
                    />
                </ion-col>
            </ion-row>
        
            <!-- CONTENUTO -->
            <ion-row class="ion-padding">
                <ion-col>
                    <ion-grid>
                        <!-- NOME -->
                        <ion-row class="section">
                            <ion-col size={12}>
                                <ion-text class="h1">{membro.nome} {membro.cognome}</ion-text>
                            </ion-col>
                        </ion-row>
        
                        <!-- INCARICO -->
                        {#if membro.incarico}
                            <ion-row class="section">
                                <ion-col size={12}>
                                    <ion-text class="h2">Incarico</ion-text>
                                </ion-col>
                                <ion-col style:padding-top="8px">
                                    <ion-text style:font-size="large">{parseMultiLanguageContent(membro.incarico)}</ion-text>
                                </ion-col>
                            </ion-row>
                        {/if}
        
                        <!-- DELEGHE -->
                        {#if membro.deleghe}   
                            <ion-row class="section">
                                <ion-col size={12}>
                                    <ion-text class="h2">Deleghe</ion-text>
                                </ion-col>
                                <ion-col style:padding-top="8px">
                                    <ion-text style:font-size="large">{parseMultiLanguageContent(membro.deleghe)}</ion-text>
                                </ion-col>
                            </ion-row>
                        {/if}
        
                        <!-- BIOGRAFIA -->
                        {#if membro.biografia}
                            <ion-row class="section">
                                <ion-col size={12}>
                                    <ion-text class="h2">Biografia e note</ion-text>
                                </ion-col>
                                <ion-col style:padding-top="8px">
                                    <ion-text style:font-size="large">{parseMultiLanguageContent(membro.biografia)}</ion-text>
                                </ion-col>
                            </ion-row>
                        {/if}
                    </ion-grid>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    ion-modal {
        --backdrop-opacity: 0.8;
        backdrop-filter: blur(1px);

        &::part(content) {
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            box-shadow: 0px 0px 20px 5px rgb(0 0 0 / 32%);
        }
    }

    :global(ion-input.ion-invalid div.error-text) {
        display: null !important;
    }

    ion-img {
        height: 256px;
        object-fit: cover;
    }
</style>
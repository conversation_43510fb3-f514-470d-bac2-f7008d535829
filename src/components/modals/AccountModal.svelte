<script>
    import { ACCOUNT_MODAL_OPEN } from '@store';
    import Icon from '@iconify/svelte';
    import { authService } from '@lib/supabase';
    import { toastController } from 'ionic-svelte';
    import { t } from '@lib/i18n';

    let breakpoints = [0, 0.5, 1];
    let isLoading = false;
    let currentUser = null;

    const handleDismiss = () => {
        ACCOUNT_MODAL_OPEN.set(false);
    };

    const showToast = async (message, color = 'danger') => {
        const toast = await toastController.create({
            message,
            duration: 2000,
            color
        });
        toast.present();
    };

    const handleGoogleLogin = async () => {
        isLoading = true;

        try {
            const { data, error } = await authService.signInWithGoogle({
                redirectTo: window.location.origin
            });

            if (error) {
                console.error('Google login error:', error);
                await showToast(`${$t('error_generic')}: ${error.message}`);
                return;
            }

            // The page will automatically redirect to Google
        } catch (error) {
            console.error('Google login error:', error);
            await showToast($t('error_generic'));
        } finally {
            isLoading = false;
        }
    }

    const handleLogout = async () => {
        isLoading = true;
        try {
            await authService.signOut();
            handleDismiss();
            await showToast($t('logout_success'), 'success');
        } catch (error) {
            console.error('Logout error:', error);
            await showToast($t('logout_error'));
        } finally {
            isLoading = false;
        }
    };

    const fetchUser = async () => {
        try {
            currentUser = await authService.getLoggedUser();
        } catch (error) {
            console.error('Error fetching user:', error);
            currentUser = null;
        }
    };

    $: if ($ACCOUNT_MODAL_OPEN) {
        fetchUser();
    }
</script>

<ion-modal
    mode="md"
    is-open={$ACCOUNT_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={0.5}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            {#if currentUser}
                <!-- USER PROFILE -->
                <ion-row class="section">
                    <ion-col size={12} class="ion-text-center">
                        <ion-text class="h1">{$t('account_title')}</ion-text>
                    </ion-col>
                </ion-row>

                <!-- USER INFO -->
                <ion-row class="section">
                    <ion-col size={12}>
                        <ion-item lines="none">
                            <ion-avatar slot="start">
                                {#if currentUser.user_metadata?.avatar_url}
                                    <img src={currentUser.user_metadata.avatar_url} alt="Profile" />
                                {:else}
                                    <div class="default-avatar">
                                        {currentUser.email[0].toUpperCase()}
                                    </div>
                                {/if}
                            </ion-avatar>
                            <ion-label>
                                <h2>{currentUser.email}</h2>
                                <p>
                                    {#if currentUser.user_metadata?.name}
                                        {currentUser.user_metadata.name}
                                    {:else}
                                        {$t('user_default')}
                                    {/if}
                                </p>
                            </ion-label>
                        </ion-item>
                    </ion-col>
                </ion-row>

                <!-- ACTIONS -->
                <ion-row>
                    <ion-col size={12}>
                        <ion-button
                            on:click={handleLogout}
                            expand="block"
                            color="danger"
                            disabled={isLoading}
                        >
                            {#if isLoading}
                                <ion-spinner name="crescent"></ion-spinner>
                            {:else}
                                <Icon icon="mdi:logout" width="20" height="20" style="margin-right: 10px;"/>
                                {$t('logout')}
                            {/if}
                        </ion-button>
                    </ion-col>
                </ion-row>
            {:else}
                <!-- TITOLO -->
                <ion-row class="section">
                    <ion-col size={12} class="ion-text-center">
                        <ion-text class="h1">{$t('login_title')}</ion-text>
                    </ion-col>
                </ion-row>

                <!-- Welcome text -->
                <ion-row class="section">
                    <ion-col size={12} class="ion-text-center">
                        <ion-text color="medium" style="font-size: large;">
                            <p>{$t('login_description')}</p>
                        </ion-text>
                    </ion-col>
                </ion-row>

                <!-- GOOGLE LOGIN BUTTON -->
                <ion-row class="section">
                    <ion-col size={12}>
                        <ion-button
                            on:click={handleGoogleLogin}
                            expand="block"
                            color="light"
                            disabled={isLoading}
                        >
                            {#if isLoading}
                                <ion-spinner name="crescent"></ion-spinner>
                            {:else}
                                <Icon icon="logos:google-icon" width="20" height="20" style="margin-right: 10px;"/>
                                {$t('login_with_google')}
                            {/if}
                        </ion-button>
                    </ion-col>
                </ion-row>
            {/if}
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    /* Drawer specific styles */
    :global(.bottom-drawer) {
        --height: 100%;
        --width: 100%;
        align-items: flex-end;
        --backdrop-opacity: 0.9;

        &::part(content) {
            border-radius: 20px 20px 0 0;
            box-shadow: 0px -4px 20px 0px rgb(0 0 0 / 15%);
        }
    }

    .divider {
        display: flex;
        align-items: center;
        text-align: center;
        margin: 20px 0;
    }

    .divider::before,
    .divider::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid var(--ion-color-medium);
    }

    .divider span {
        padding: 0 10px;
        color: var(--ion-color-medium);
        font-size: 14px;
    }

    .default-avatar {
        width: 100%;
        height: 100%;
        background: var(--ion-color-primary);
        color: var(--ion-color-primary-contrast);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        border-radius: 50%;
    }

    .welcome-logo {
        width: 120px;
        height: auto;
        margin: 20px 0;
    }

    .section {
        margin-bottom: 20px;
    }
</style>
<script>
    import { PREFERENCES_MODAL_OPEN } from '@store';
    import { toastController } from 'ionic-svelte';
    import { userLanguage, notificationsEmail, notificationsPush } from '@lib/stores/preferences';
    import { t, getAvailableLanguages } from '@lib/i18n';
    /* import { PushNotifications } from '@capacitor/push-notifications'; */

    let breakpoints = [0, 0.9];

    // Use the persistent stores for preferences
    let notificheEmail;
    let notifichePush;
    let selectedLanguage;

    // Get available languages
    const availableLanguages = getAvailableLanguages();

    // Subscribe to the stores
    notificationsEmail.subscribe(value => {
        notificheEmail = value;
    });

    notificationsPush.subscribe(value => {
        notifichePush = value;
    });

    userLanguage.subscribe(value => {
        selectedLanguage = value;
    });

    const showToast = async (message, color = 'danger') => {
        const toast = await toastController.create({
            message,
            duration: 2000,
            color
        });
        toast.present();
    };

    const handleDismiss = () => {
        PREFERENCES_MODAL_OPEN.set(false);
    };

    const updatePreferences = async () => {
        // Save preferences to persistent stores
        userLanguage.set(selectedLanguage);
        notificationsEmail.set(notificheEmail);
        notificationsPush.set(notifichePush);

        // Request Push Notification permission
        /* await PushNotifications.requestPermissions().then(result => {
            if (result.receive === 'granted') {
                // User granted permission
                console.log('Push notification permission granted');
                notifichePush = true;
                notificationsPush.set(true);
            } else {
                // User denied permission
                console.log('Push notification permission denied');
                notifichePush = false;
                notificationsPush.set(false);
            }
        }); */

        await showToast($t('preferences_saved'), 'success');
        PREFERENCES_MODAL_OPEN.set(false);
    }
</script>

<ion-modal
    mode="md"
    is-open={$PREFERENCES_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={0.9}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            <!-- TITLE -->
            <ion-row class="section">
                <ion-col size={12} class="ion-text-center">
                    <ion-text class="h1">{$t('preferences_title')}</ion-text>
                    <p class="subtitle">{$t('preferences_subtitle')}</p>
                </ion-col>
            </ion-row>

            <!-- LANGUAGE SELECTION -->
            <ion-row>
                <ion-col size={12}>
                    <ion-list>
                        <ion-list-header style:padding-left="0px">
                            <ion-label>
                                <ion-text class="h2">{$t('language_section')}</ion-text>
                            </ion-label>
                        </ion-list-header>

                        <ion-item>
                            <ion-select
                                label={$t('language_select')}
                                value={selectedLanguage}
                                on:ionChange={(e) => {
                                    selectedLanguage = e.detail.value;
                                    userLanguage.set(e.detail.value);
                                }}>
                                {#each availableLanguages as language}
                                    <ion-select-option value={language.code}>{language.name}</ion-select-option>
                                {/each}
                                <!-- <ion-select-option value="fr" disabled>Français</ion-select-option>
                                <ion-select-option value="de" disabled>Deutsch</ion-select-option>
                                <ion-select-option value="es" disabled>Español</ion-select-option> -->
                            </ion-select>
                        </ion-item>
                    </ion-list>
                </ion-col>
            </ion-row>

            <!-- NOTIFICATIONS FORM -->
            <ion-row>
                <ion-col size={12}>
                    <ion-list>
                        <ion-list-header style:padding-left="0px">
                            <ion-label>
                                <ion-text class="h2">{$t('notifications_section')}</ion-text>
                            </ion-label>
                        </ion-list-header>

                        <ion-item>
                            <ion-label>
                                <h2>{$t('notifications_email_title')}</h2>
                                <p>{$t('notifications_email_desc')}</p>
                            </ion-label>
                            <ion-toggle
                                slot="end"
                                checked={notificheEmail}
                                on:ionChange={(e) => {
                                    notificheEmail = e.detail.checked;
                                    notificationsEmail.set(e.detail.checked);
                                }}
                            />
                        </ion-item>

                        <!-- <ion-item>
                            <ion-label>
                                <h2>{$t('notifications_push_title')}</h2>
                                <p>{$t('notifications_push_desc')}</p>
                            </ion-label>
                            <ion-toggle
                                slot="end"
                                checked={notifichePush}
                                on:ionChange={(e) => {
                                    notifichePush = e.detail.checked;
                                    notificationsPush.set(e.detail.checked);
                                }}
                            />
                        </ion-item> -->
                    </ion-list>
                </ion-col>
            </ion-row>

            <!-- SYSTEM PERMISSIONS -->
            <!-- <ion-row>
                <ion-col size={12}>
                    <ion-list>
                        <ion-list-header style:padding-left="0px">
                            <ion-label>
                                <ion-text class="h2">Permessi di Sistema</ion-text>
                            </ion-label>
                        </ion-list-header>

                        <ion-item>
                            <ion-label>
                                <h2>Posizione</h2>
                                <p>Accesso alla posizione del dispositivo</p>
                            </ion-label>
                            <ion-toggle
                                slot="end"
                                checked={locationPermission}
                                on:ionChange={(e) => locationPermission = e.detail.checked}
                            />
                        </ion-item>

                        <ion-item>
                            <ion-label>
                                <h2>Archiviazione</h2>
                                <p>Accesso all'archiviazione del dispositivo</p>
                            </ion-label>
                            <ion-toggle
                                slot="end"
                                checked={storagePermission}
                                on:ionChange={(e) => storagePermission = e.detail.checked}
                            />
                        </ion-item>
                    </ion-list>
                </ion-col>
            </ion-row> -->

            <ion-row>
                <ion-col size={12}>
                    <ion-button expand="block" on:click={updatePreferences}>
                        {$t('save_preferences')}
                    </ion-button>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    .subtitle {
        margin-top: 8px;
        margin-bottom: 8px;
        color: var(--ion-color-medium);
        font-size: large;
        line-height: 1.4;
    }

    :global(ion-button) {
        margin-top: 24px;
        --border-radius: 12px;
        --box-shadow: none;
        height: 48px;
        font-weight: 600;
    }

    :global(ion-item) {
        --padding-start: 0;
        --inner-padding-end: 0;
        --background: transparent;
    }

    :global(ion-list) {
        background: transparent;
    }

    :global(ion-toggle) {
        padding-right: 0;
    }

    :global(.select-wrapper) {
        padding-left: 0px !important;
    }
</style>

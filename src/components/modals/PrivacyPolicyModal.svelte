<script>
    import { PRIVACY_POLICY_MODAL_OPEN } from '@store';
    import { t } from '@lib/i18n';

    let breakpoints = [0, 0.7, 1];

    const handleDismiss = () => {
        PRIVACY_POLICY_MODAL_OPEN.set(false);
    };
</script>

<ion-modal
    mode="md"
    is-open={$PRIVACY_POLICY_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={0.7}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            <!-- TITLE -->
            <ion-row class="section">
                <ion-col size={12} class="ion-text-center">
                    <ion-text class="h1">{$t('privacy_title')}</ion-text>
                    <p class="subtitle">{$t('privacy_subtitle')}</p>
                </ion-col>
            </ion-row>

            <!-- CONTENT -->
            <ion-row>
                <ion-col>
                    <div class="privacy-content">
                        <ion-text class="h2">{$t('privacy_section1_title')}</ion-text>
                        <p>{$t('privacy_section1_content')}</p>
                        <ul>
                            <li>{$t('privacy_section1_item1')}</li>
                            <li>{$t('privacy_section1_item2')}</li>
                            <li>{$t('privacy_section1_item3')}</li>
                            <li>{$t('privacy_section1_item4')}</li>
                        </ul>

                        <ion-text class="h2">{$t('privacy_section2_title')}</ion-text>
                        <p>{$t('privacy_section2_content')}</p>
                        <ul>
                            <li>{$t('privacy_section2_item1')}</li>
                            <li>{$t('privacy_section2_item2')}</li>
                            <li>{$t('privacy_section2_item3')}</li>
                            <li>{$t('privacy_section2_item4')}</li>
                            <li>{$t('privacy_section2_item5')}</li>
                        </ul>

                        <ion-text class="h2">{$t('privacy_section3_title')}</ion-text>
                        <p>{$t('privacy_section3_content')}</p>
                        <ul>
                            <li>{$t('privacy_section3_item1')}</li>
                            <li>{$t('privacy_section3_item2')}</li>
                            <li>{$t('privacy_section3_item3')}</li>
                        </ul>
                        <p>{$t('privacy_section3_item4')}</p>

                        <ion-text class="h2">{$t('privacy_section4_title')}</ion-text>
                        <p>{$t('privacy_section4_content')}</p>
                        <ul>
                            <li>{$t('privacy_section4_item1')}</li>
                            <li>{$t('privacy_section4_item2')}</li>
                            <li>{$t('privacy_section4_item3')}</li>
                        </ul>

                        <ion-text class="h2">{$t('privacy_section5_title')}</ion-text>
                        <p>{$t('privacy_section5_content')}</p>
                        <ul>
                            <li>{$t('privacy_section5_item1')}</li>
                            <li>{$t('privacy_section5_item2')}</li>
                            <li>{$t('privacy_section5_item3')}</li>
                            <li>{$t('privacy_section5_item4')}</li>
                        </ul>

                        <ion-text class="h2">{$t('privacy_section6_title')}</ion-text>
                        <p>{$t('privacy_section6_content')}</p>
                        <ul>
                            <li>{$t('privacy_section6_item1')}</li>
                            <li>{$t('privacy_section6_item2')}</li>
                            <li>{$t('privacy_section6_item3')}</li>
                            <li>{$t('privacy_section6_item4')}</li>
                            <li>{$t('privacy_section6_item5')}</li>
                        </ul>

                        <ion-text class="h2">{$t('privacy_section7_title')}</ion-text>
                        <p>{$t('privacy_section7_content')}</p>
                        <ul>
                            <li>{$t('privacy_section7_item1')}</li>
                            <li>{$t('privacy_section7_item2')}</li>
                            <li>{$t('privacy_section7_item3')}</li>
                            <li>{$t('privacy_section7_item4')}</li>
                        </ul>

                        <ion-text class="h2">{$t('privacy_section8_title')}</ion-text>
                        <p>{$t('privacy_section8_content')}</p>

                        <ion-text class="h2">{$t('privacy_section9_title')}</ion-text>
                        <p>{$t('privacy_section9_content')}</p>
                        <p>Email: <EMAIL></p>
                        <p>Indirizzo: Viale Savoia 12, 73025 Martano (LE)</p>
                    </div>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    .subtitle {
        margin-top: 8px;
        margin-bottom: 8px;
        color: var(--ion-color-medium);
        font-size: large;
        line-height: 1.4;
    }
</style>

<script>
    import { ARTISTA_MODAL_OPEN, ARTISTA_MODAL_PROPS } from '@store';
    import Icon from '@iconify/svelte';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    let breakpoints = [0, 0.9, 1];
    let artista = {};

    const handleOpen = () => {
        artista = $ARTISTA_MODAL_PROPS?.artista || {};
    }

    const handleDismiss = () => {
        ARTISTA_MODAL_PROPS.set({});
        ARTISTA_MODAL_OPEN.set(false);
    };
</script>

<ion-modal
is-open={$ARTISTA_MODAL_OPEN}
initial-breakpoint="0.9"
{breakpoints}
on:ionModalDidDismiss={handleDismiss}
on:ionModalWillPresent={handleOpen}>
    <ion-content>
        <ion-grid class="ion-no-padding">
            <!-- COPERTINA -->
            <ion-row>
                <ion-col>
                    <ion-img 
                        loading="lazy"
                        src={artista.anteprima || 'placeholder.jpg'} 
                        alt="copertina"
                        on:ionError={(e) => {
                            e.target.src = 'placeholder.jpg';
                        }}
                    />
                </ion-col>
            </ion-row>
        
            <!-- CONTENUTO -->
            <ion-row class="ion-padding">
                <ion-col>
                    <ion-grid>
                        <!-- NOME -->
                        <ion-row class="section">
                            <ion-col size={12}>
                                <ion-text class="h1">{artista.nome}</ion-text>
                            </ion-col>
                        </ion-row>

                        <!-- INFO E CONTATTI -->
                        <ion-row class="section">
                            {#if artista.data_nascita}
                                <ion-col size={12} style:padding-top="8px">
                                    <div class="contact-row">
                                        <Icon icon="ion:calendar-outline" width={20}/>
                                        <ion-text style:font-size="large">
                                            {new Date(artista.data_nascita).toLocaleDateString('it-IT', {
                                                day: '2-digit',
                                                month: '2-digit',
                                                year: 'numeric'
                                            })}
                                        </ion-text>
                                    </div>
                                </ion-col>
                            {/if}
                            {#if artista.numero_telefono}
                                <ion-col size={12} style:padding-top="8px">
                                    <div class="contact-row">
                                        <Icon icon="ion:call-outline" width={20}/>
                                        <ion-text style:font-size="large">
                                            <a href={'tel:' + (artista.numero_telefono.startsWith('+') ? '' : '+39 ') + artista.numero_telefono} 
                                               style:color="var(--ion-color-primary-foreground)">
                                                {artista.numero_telefono.startsWith('+') ? '' : '+39 '}{artista.numero_telefono}
                                            </a>
                                        </ion-text>
                                    </div>
                                </ion-col>
                            {/if}
                            {#if artista.email}
                                <ion-col size={12} style:padding-top="8px">
                                    <div class="contact-row">
                                        <Icon icon="ion:mail-outline" width={20}/>
                                        <ion-text style:font-size="large">
                                            <a href={'mailto:' + artista.email} 
                                               style:color="var(--ion-color-primary-foreground)">
                                                {artista.email}
                                            </a>
                                        </ion-text>
                                    </div>
                                </ion-col>
                            {/if}
                        </ion-row>
        
                        <!-- BIOGRAFIA -->
                        {#if artista.biografia}
                            <ion-row class="section">
                                <ion-col size={12}>
                                    <ion-text class="h2">Biografia</ion-text>
                                </ion-col>
                                <ion-col style:padding-top="8px">
                                    <ion-text style:font-size="large">
                                        {parseMultiLanguageContent(artista.biografia)}
                                    </ion-text>
                                </ion-col>
                            </ion-row>
                        {/if}
                    </ion-grid>
                </ion-col>
            </ion-row>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    ion-modal {
        --backdrop-opacity: 0.8;
        backdrop-filter: blur(1px);

        &::part(content) {
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
            box-shadow: 0px 0px 20px 5px rgb(0 0 0 / 32%);
        }
    }

    ion-img {
        height: 256px;
        object-fit: cover;
    }

    .contact-row {
        display: flex;
        align-items: center;
        gap: 8px;
    }
</style>

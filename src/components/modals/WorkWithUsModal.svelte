<script>
    import { WORK_WITH_US_MODAL_OPEN } from '@store';
    import Icon from '@iconify/svelte';
    import { t } from '@lib/i18n';

    let nome, cognome, email, messaggio, telefono, paese_residenza, cap, titoli_studio, esperienze_lavorative, competenze, disponibilita, richieste;
    let breakpoints = [0, 1];

    const handleDismiss = () => {
        nome = null;
        cognome = null;
        email = null;
        messaggio = null;
        telefono = null;
        paese_residenza = null;
        cap = null;
        titoli_studio = null;
        esperienze_lavorative = null;
        competenze = null;
        disponibilita = null;
        richieste = null;
        WORK_WITH_US_MODAL_OPEN.set(false);
    };

    const validateEmail = (e) => {
        const input = e.target;
        const value = e.target.value;

        input.classList.remove('ion-touched');
        input.classList.remove('ion-valid');
        input.classList.remove('ion-invalid');

        const isValidEmail = value.match(/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/)
        isValidEmail ? input.classList.add('ion-valid') : input.classList.add('ion-invalid');

        if(value) input.classList.add('ion-touched');
    }
</script>

<ion-modal
    mode="md"
    is-open={$WORK_WITH_US_MODAL_OPEN}
    on:ionModalDidDismiss={handleDismiss}
    breakpoints={breakpoints}
    initial-breakpoint={1}
    class="bottom-drawer">
    <ion-content>
        <ion-grid class="ion-padding">
            <!-- TITLE -->
            <ion-row class="section">
                <ion-col size={12} class="ion-text-center">
                    <ion-text class="h1">{$t('work_with_us_title')}</ion-text>
                    <p class="subtitle">{$t('work_with_us_subtitle')}</p>
                </ion-col>
            </ion-row>

            <!-- CONTACT FORM -->
            <form class="contact-form">
                <ion-row>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Mario"
                            label={$t('work_with_us_name') + '*'}
                            label-placement="stacked"
                            name="nome"
                            type="text"
                            value={nome}
                        />
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Rossi"
                            label={$t('work_with_us_surname') + '*'}
                            label-placement="stacked"
                            name="cognome"
                            type="text"
                            value={cognome}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="YYYY-MM-DD"
                            label="Data di nascita*"
                            label-placement="stacked"
                            name="data_nascita"
                            type="date"
                        />
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="Città/paese"
                            label={$t('work_with_us_residence') + '*'}
                            label-placement="stacked"
                            name="paese_residenza"
                            type="text"
                            value={paese_residenza}
                        />
                    </ion-col>
                    <ion-col size={6} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="12345"
                            label={$t('work_with_us_zip') + '*'}
                            label-placement="stacked"
                            name="cap"
                            type="text"
                            value={cap}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="+39 ************"
                            label={$t('work_with_us_phone') + '*'}
                            label-placement="stacked"
                            name="telefono"
                            type="tel"
                            value={telefono}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-input
                            required
                            fill="outline"
                            placeholder="<EMAIL>"
                            label={$t('work_with_us_email') + '*'}
                            label-placement="stacked"
                            error-text={$t('error_invalid_email')}
                            name="email"
                            type="email"
                            value={email}
                            on:ionInput={validateEmail}
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            required
                            fill="outline"
                            placeholder={$t('work_with_us_education')}
                            label={$t('work_with_us_education') + '*'}
                            label-placement="stacked"
                            name="titoli_studio"
                            type="text"
                            value={titoli_studio}
                            style:min-height="80px"
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            required
                            fill="outline"
                            placeholder={$t('work_with_us_experience')}
                            label={$t('work_with_us_experience') + '*'}
                            label-placement="stacked"
                            name="esperienze_lavorative"
                            type="text"
                            value={esperienze_lavorative}
                            style:min-height="80px"
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            required
                            fill="outline"
                            placeholder={$t('work_with_us_skills')}
                            label={$t('work_with_us_skills') + '*'}
                            label-placement="stacked"
                            name="competenze"
                            type="text"
                            value={competenze}
                            style:min-height="80px"
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            required
                            fill="outline"
                            placeholder={$t('work_with_us_availability')}
                            label={$t('work_with_us_availability') + '*'}
                            label-placement="stacked"
                            name="disponibilita"
                            type="text"
                            value={disponibilita}
                            style:min-height="80px"
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            required
                            fill="outline"
                            placeholder={$t('work_with_us_requests')}
                            label={$t('work_with_us_requests') + '*'}
                            label-placement="stacked"
                            name="richieste"
                            type="text"
                            value={richieste}
                            style:min-height="80px"
                        />
                    </ion-col>
                    <ion-col size={12} class="ion-padding-bottom">
                        <ion-textarea
                            required
                            fill="outline"
                            label={$t('work_with_us_cover_letter') + '*'}
                            label-placement="stacked"
                            placeholder={$t('work_with_us_cover_letter')}
                            name="messaggio"
                            value={messaggio}
                            style:min-height="150px"
                        />
                    </ion-col>
                    <ion-col size={12}>
                        <ion-button expand="block" type="submit">
                            {$t('work_with_us_send')}
                            <Icon
                                icon="ion:paper-plane"
                                slot="end"
                                width="20"
                                height="20"
                                style="margin-left: 8px"
                            />
                        </ion-button>
                    </ion-col>
                </ion-row>
            </form>
        </ion-grid>
    </ion-content>
</ion-modal>

<style>
    ion-content {
        &::before {
            content: none;
        }

        &::after {
            content: none;
        }
    }

    .subtitle {
        margin-top: 8px;
        color: var(--ion-color-medium);
        font-size: large;
        line-height: 1.4;
    }

    .contact-form {
        width: 100%;
    }

    .contact-form ion-button {
        margin-top: 8px;
        --border-radius: 12px;
        --box-shadow: none;
        height: 48px;
        font-weight: 600;
    }

    .contact-form ion-button::part(native) {
        padding-right: 16px;
    }

    /* Add this new global style */
    .contact-form ion-input[type="date"]::part(placeholder) {
        color: var(--ion-color-light);
        opacity: 0.5;
    }
</style>

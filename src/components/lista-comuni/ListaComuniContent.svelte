<script>
    import { onMount } from "svelte";
    import { getAnteprimaComune, selectComuni } from "@lib/supabase";
    import { navController } from "ionic-svelte";
    import Comune from "@pages/Comune.svelte";
    import Icon from '@iconify/svelte';
    import { t } from '@lib/i18n';

    let allComuni = [];
    $: comuni = [];
    let currentLetter = '';

    // Generate alphabet array A-Z
    const alphabet = Array.from('ABCDEFGHIJKLMNOPQRSTUVWXYZ');

    onMount(async () => {
        allComuni = await selectComuni() || [];
        for (const comune of allComuni) {
            const imageUrl = await getAnteprimaComune(comune.nome);
            comune.anteprima = imageUrl;
            comuni.push(comune);
        }
        // Sort comuni alphabetically
        comuni = comuni.sort((a, b) => a.nome.localeCompare(b.nome));
    })

    const handleSearchComune = async (e) => {
        const query = e.target.value.trim().toLowerCase();
        comuni = query
            ? allComuni.filter(comune => comune.nome.toLowerCase().startsWith(query))
            : allComuni;
    }

    const scrollToLetter = (letter) => {
        currentLetter = letter;
        const element = document.getElementById(`letter-${letter}`);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // Group comuni by first letter
    $: groupedComuni = comuni.reduce((groups, comune) => {
        const letter = comune.nome[0].toUpperCase();
        if (!groups[letter]) {
            groups[letter] = [];
        }
        groups[letter].push(comune);
        return groups;
    }, {});
</script>



<div class="sticky-header">
    <ion-searchbar
        class="ion-no-padding"
        on:ionInput={handleSearchComune}
        placeholder={$t('search_municipality')}
        animated={true}
    />
</div>

<div class="content-wrapper">
    <div class="comuni-list">
        {#each Object.entries(groupedComuni) as [letter, letterComuni]}
            <div class="letter-section" id="letter-{letter}">
                <div class="letter-header">{letter}</div>
                {#each letterComuni as comune}
                    <div class="comune-item" on:click={() => navController.push(Comune, {comune})}>
                        <ion-img
                            src={comune.anteprima}
                            alt={comune.nome}
                            loading="lazy"
                            on:ionError={(e) => {
                                e.target.src = 'placeholder.jpg';
                            }}
                        />
                        <div class="comune-details">
                            <h2>{comune.nome}</h2>
                            <p>{$t('province_of')} {comune.provincia}</p>
                        </div>
                    </div>
                {/each}
            </div>
        {/each}
    </div>

    <div class="alphabet-index">
        {#each alphabet as letter}
            {#if groupedComuni[letter]}
                <div
                    class="letter {currentLetter === letter ? 'active' : ''}"
                    on:click={() => scrollToLetter(letter)}
                >
                    {letter}
                </div>
            {/if}
        {/each}
    </div>
</div>

<style>
    .content-wrapper {
        position: relative;
        display: flex;
    }

    .comuni-list {
        flex: 1;
        padding: 1rem;
        padding-right: 3rem;
    }

    .letter-section {
        margin-bottom: 1rem;
    }

    .letter-header {
        font-size: 1rem;
        font-weight: 600;
        color: var(--ion-color-primary);
        padding: 0.5rem 0;
        position: sticky;
        top: 65px;
        background: var(--ion-background-color);
        z-index: 1;
    }

    .alphabet-index {
        position: fixed;
        right: 5px;
        top: calc(130px + (100vh - 130px - 60vh) / 2);
        height: 60vh;
        transform: none;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        padding: 0.75rem 0.5rem;
        font-size: 14px;
        z-index: 1000;
        backdrop-filter: blur(4px);
        border-radius: 20px;
    }

    .alphabet-index .letter {
        padding: 3px 8px;
        color: var(--ion-color-primary);
        cursor: pointer;
        text-align: center;
        min-width: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .alphabet-index .letter.active {
        font-weight: bold;
        background: var(--ion-color-primary);
        color: white;
        border-radius: 8px;
    }

    .sticky-header {
        position: sticky;
        top: 0;
        background: var(--ion-background-color);
        z-index: 1000;
        padding: 1rem;
        border-bottom: 1px solid var(--ion-color-light);
    }

    .comune-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: transform 0.2s ease;
    }

    .comune-item:active {
        transform: scale(0.98);
    }

    .comune-item ion-img {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        object-fit: fill;
    }

    .comune-details {
        flex: 1;
        margin-left: 1rem;
    }

    .comune-details h2 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--ion-color-dark);
    }

    .comune-details p {
        margin: 0.25rem 0 0 0;
        font-size: 0.9rem;
        color: var(--ion-color-medium);
    }
</style>
<script>
    import { getAnteprimaVideo } from '@lib/supabase';
    import { navController } from 'ionic-svelte';
    import Icon from '@iconify/svelte';
    import { onMount } from 'svelte';
    import { toastController } from 'ionic-svelte';
    import { Browser } from '@capacitor/browser';
    import YoutubePlayer from '@components/youtube/YoutubePlayer.svelte';
    import { extractYoutubeVideoId } from '@lib/utils/youtube';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    export let comune;
    let video = [];
    let infiniteScroll;
    let isLoading;
    let expandedVideoId = null;

    onMount(async () => {
        isLoading = true;
        await appendItems(0, 4);
        isLoading = false;
    })

    const infiniteAction = async () => {
        await appendItems(video.length, video.length + 3);
        infiniteScroll.complete();
    };

    const appendItems = async (startIndex, endIndex) => {
        const videoToAdd = comune.video.slice(startIndex, endIndex) || [];

        if(videoToAdd.length == 0){
            infiniteScroll.disabled = true;
            return;
        }

        for (const videoItem of videoToAdd) {
            const imageUrl = await getAnteprimaVideo(videoItem.id);
            videoItem.anteprima = imageUrl;
            // Extract YouTube video ID
            videoItem.youtubeId = extractYoutubeVideoId(videoItem.link);
        }

        video = video.concat(videoToAdd);
    }

    const openLink = async (link) => {
        try {
            if (!link.startsWith('http://') && !link.startsWith('https://')) {
                link = 'https://' + link;
            }
            await Browser.open({ url: link });
        } catch (error) {
            const toast = await toastController.create({
                message: 'Errore nell\'apertura del link: ' + error.message,
                duration: 2000,
                position: 'bottom',
                color: 'danger'
            });
            await toast.present();
        }
    }

    const toggleVideoExpand = (videoItem) => {
        if (expandedVideoId === videoItem.id) {
            expandedVideoId = null;
        } else {
            expandedVideoId = videoItem.id;
        }
    }
</script>

<ion-grid class="ion-padding main-grid">
    <!-- TITLE -->
    <ion-row>
        <ion-col>
            <ion-text class="h1">I video di {comune.nome}</ion-text>
        </ion-col>
    </ion-row>

    <ion-row>
        <!-- VIDEO -->
        {#if isLoading}
            {#each Array(4) as _}
                <ion-col size={12}>
                    <ion-card>
                        <ion-skeleton-text
                            animated
                            style="height: 160px; margin: 0; border-radius: 0;"
                        />
                        <ion-card-content>
                            <ion-skeleton-text
                                animated
                                style="width: 80%; height: 24px; margin-bottom: 8px;"
                            />
                            <ion-skeleton-text
                                animated
                                style="width: 60%; height: 18px;"
                            />
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            {/each}
        {:else}
            {#each video as videoItem}
                <ion-col size={12}>
                    <ion-card on:click={() => toggleVideoExpand(videoItem)}>
                        {#if expandedVideoId === videoItem.id && videoItem.youtubeId}
                            <div class="youtube-player-wrapper">
                                <YoutubePlayer
                                    videoId={videoItem.youtubeId}
                                    height="240px"
                                    on:error={() => expandedVideoId = null}
                                />
                            </div>
                        {:else}
                            <div class="card-image-container">
                                <ion-img
                                    loading="lazy"
                                    src={videoItem.anteprima || 'placeholder.jpg'}
                                    alt="copertina"
                                    on:ionError={(e) => {
                                        e.target.src = 'placeholder.jpg';
                                    }}
                                />
                                <div class="youtube-icon">
                                    <Icon icon="logos:youtube-icon" width={50} />
                                </div>
                            </div>
                        {/if}
                        <ion-card-content>
                            <h2 class="video-title">{videoItem.titolo}</h2>
                            <p class="video-description">{parseMultiLanguageContent(videoItem.descrizione)}</p>
                            {#if expandedVideoId === videoItem.id && !videoItem.youtubeId}
                                <ion-button expand="block" fill="clear" on:click|stopPropagation={() => openLink(videoItem.link)}>
                                    <ion-text>Guarda su YouTube</ion-text>
                                </ion-button>
                            {/if}
                        </ion-card-content>
                    </ion-card>
                </ion-col>
            {:else}
                <ion-col class="ion-padding">
                    <ion-text style:font-size="large">Non ci sono video attualmente disponibili</ion-text>
                </ion-col>
            {/each}
        {/if}
    </ion-row>

    <ion-infinite-scroll on:ionInfinite={infiniteAction} threshold="100px" bind:this={infiniteScroll}>
        <ion-infinite-scroll-content loading-spinner="bubbles"/>
    </ion-infinite-scroll>
</ion-grid>

<style>
    ion-card {
        margin: 8px 0;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    ion-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    }

    .card-image-container {
        position: relative;
        height: 160px;
    }

    .youtube-player-wrapper {
        width: 100%;
        height: 240px;
        background-color: #000;
    }

    ion-img {
        height: 100%;
        width: 100%;
        object-fit: cover;
    }

    ion-card-content {
        padding: 16px;
    }

    .video-title {
        margin: 0 0 8px 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: #000000;
        line-height: 1.4;
    }

    .video-description {
        margin: 0;
        font-size: 0.9rem;
        color: var(--ion-color-medium);
        line-height: 1.5;
    }

    .youtube-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 2;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
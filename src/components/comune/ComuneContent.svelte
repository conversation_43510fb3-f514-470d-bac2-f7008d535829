<script>
    import { MEMBRO_GIUNTA_MODAL_OPEN, MEMBRO_GIUNTA_MODAL_PROPS, EVENTO_MODAL_OPEN, EVENTO_MODAL_PROPS } from '@store';
    import { selectGiuntaComunaleByComuneId, getFotoMembroGiuntaComunale, selectCategorieByTipo, getAnteprimaCategoria, selectEventiByComuneId, updateComuneCoordinates, getAnteprimaEvento, selectVideoComuneById } from '@lib/supabase';
    import { navController } from 'ionic-svelte';
    import Categoria from '@pages/Categoria.svelte';
    import ListaCategorie from '@pages/ListaCategorie.svelte';
    import VideoComune from '@pages/VideoComune.svelte';
    import Eventi from '@pages/Eventi.svelte';
    import Icon from '@iconify/svelte';
    import { onMount, onDestroy } from "svelte";
    import { Splide, SplideSlide } from '@splidejs/svelte-splide';
    import '@splidejs/svelte-splide/css';
    import { Map, TileLayer, Marker, Popup } from 'sveaflet';
    import { toastController, alertController } from 'ionic-svelte';
    import { Browser } from '@capacitor/browser';
    import WeatherWidget from '@components/weather/WeatherWidget.svelte';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';
    import { t } from '@lib/i18n';

    export let comune;
    let allMembriGiuntaComunale = [], allserviziEssenziali = [], allArteCulturaETurismo = [], allServiziCommerciali = [], allEventi = [];
    $: membriGiuntaComunale = [];
    $: emergenze = [];
    $: serviziEssenziali = [];
    $: arteCulturaETurismo = [];
    $: serviziCommerciali = [];
    $: eventi = [];

    // Get the WeatherAPI.com API key from environment variables
    const WEATHER_API_KEY = import.meta.env.VITE_WEATHER_API_KEY;
    let weatherData = null;

    const mesiMap = {
        1: "Gennaio",
        2: "Febbraio",
        3: "Marzo",
        4: "Aprile",
        5: "Maggio",
        6: "Giugno",
        7: "Luglio",
        8: "Agosto",
        9: "Settembre",
        10: "Ottobre",
        11: "Novembre",
        12: "Dicembre"
    }

    const sliderOptionsGiunta = {
        type: "slide",
        gap: "0.1rem",
        arrows: false,
        pagination:false,
        autoWidth: true
    }

    const sliderOptionsCategorie = {
        type: "slide",
        gap: "0.1rem",
        arrows: false,
        pagination:false,
        autoWidth: true
    }

    const sliderOptionsEventi = {
        type: "slide",
        gap: "0.15rem",
        arrows: false,
        pagination:false,
        autoWidth: true
    }

    let mapElement;
    let latitude = null;
    let longitude = null;
    let mapReady = false;
    let mapOptions = null;

    let emergenzeLoading = true;
    let emergenzeTimeout;

    let serviziEssenzialiLoading = true;
    let serviziEssenzialiTimeout;

    let arteCulturaETurismoLoading = true;
    let arteCulturaETurismoTimeout;

    let serviziCommercialiLoading = true;
    let serviziCommercialiTimeout;

    async function updateCoordinates() {
        if (comune.latitude && comune.longitude) {
            latitude = parseFloat(comune.latitude);
            longitude = parseFloat(comune.longitude);
        } else {
            const coords = await geocodeAddress(comune.nome + ', Italy');
            if (coords) {
                const { error } = await updateComuneCoordinates(comune.id, coords.lat, coords.lon);
                if (!error) {
                    latitude = parseFloat(coords.lat);
                    longitude = parseFloat(coords.lon);
                }
            }
        }

        mapOptions = {
            center: [latitude, longitude],
            zoom: 14,
            invalidateSize: true,
            zoomControl: true,
            fadeAnimation: false,
            zoomAnimation: false,
            markerZoomAnimation: false,
            trackResize: true,
            preferCanvas: true
        };

        setTimeout(() => {
            mapReady = true;

            setTimeout(() => {
                if (mapElement?.map) {
                    mapElement.map.invalidateSize();
                    mapElement.map.setView([latitude, longitude], 14);
                }
            }, 250);
        }, 100);
    }

    async function geocodeAddress(address) {
        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`);
            const data = await response.json();

            if (data && data[0]) {
                return {
                    lat: parseFloat(data[0].lat),
                    lon: parseFloat(data[0].lon)
                };
            }
            return null;
        } catch (error) {
            console.error('Geocoding error:', error);
            return null;
        }
    }

    onMount(async () => {
        await updateCoordinates();
        allMembriGiuntaComunale = await selectGiuntaComunaleByComuneId(comune.id);
        comune.video = await selectVideoComuneById(comune.id);

        for (const membro of allMembriGiuntaComunale) {
            const imageUrl = await getFotoMembroGiuntaComunale(comune.nome, membro);
            membro.foto = imageUrl;
            membriGiuntaComunale.push(membro);
            membriGiuntaComunale = membriGiuntaComunale;
        }

        // Add timeout for emergenze
        emergenzeTimeout = setTimeout(() => {
            if (emergenze.length === 0) {
                emergenzeLoading = false;
            }
        }, 5000);

        emergenze = await selectCategorieByTipo('Emergenze', comune.categorie_da_escludere) || [];
        emergenzeLoading = false;
        clearTimeout(emergenzeTimeout);

        // Add timeout for servizi essenziali
        serviziEssenzialiTimeout = setTimeout(() => {
            if (serviziEssenziali.length === 0) {
                serviziEssenzialiLoading = false;
            }
        }, 5000);

        allserviziEssenziali = await selectCategorieByTipo('Servizi essenziali', comune.categorie_da_escludere, 4) || [];

        for (const categoria of allserviziEssenziali) {
            const imageUrl = await getAnteprimaCategoria(categoria.nome);
            categoria.anteprima = imageUrl || 'placeholder.jpg';
            serviziEssenziali.push(categoria);
            serviziEssenziali = serviziEssenziali;
        }
        serviziEssenzialiLoading = false;
        clearTimeout(serviziEssenzialiTimeout);

        // Add timeout for arte cultura e turismo
        arteCulturaETurismoTimeout = setTimeout(() => {
            if (arteCulturaETurismo.length === 0) {
                arteCulturaETurismoLoading = false;
            }
        }, 5000);

        allArteCulturaETurismo = await selectCategorieByTipo('Arte, cultura e turismo', comune.categorie_da_escludere, 4) || [];

        for (const categoria of allArteCulturaETurismo) {
            const imageUrl = await getAnteprimaCategoria(categoria.nome);
            categoria.anteprima = imageUrl || 'placeholder.jpg';
            arteCulturaETurismo.push(categoria);
            arteCulturaETurismo = arteCulturaETurismo;
        }
        arteCulturaETurismoLoading = false;
        clearTimeout(arteCulturaETurismoTimeout);

        // Add timeout for servizi commerciali
        serviziCommercialiTimeout = setTimeout(() => {
            if (serviziCommerciali.length === 0) {
                serviziCommercialiLoading = false;
            }
        }, 5000);

        allServiziCommerciali = await selectCategorieByTipo('Servizi commerciali', comune.categorie_da_escludere, 4) || [];

        for (const categoria of allServiziCommerciali) {
            const imageUrl = await getAnteprimaCategoria(categoria.nome);
            categoria.anteprima = imageUrl || 'placeholder.jpg';
            serviziCommerciali.push(categoria);
            serviziCommerciali = serviziCommerciali;
        }
        serviziCommercialiLoading = false;
        clearTimeout(serviziCommercialiTimeout);

        allEventi = await selectEventiByComuneId(comune.id) || [];

        for (const evento of allEventi) {
            const imageUrl = await getAnteprimaEvento(evento.id);
            if (imageUrl) {
                evento.anteprima = imageUrl;
            }
        }

        eventi = allEventi.slice(0, 4);
    })

    onDestroy(() => {
        if (emergenzeTimeout) {
            clearTimeout(emergenzeTimeout);
        }
        if (serviziEssenzialiTimeout) {
            clearTimeout(serviziEssenzialiTimeout);
        }
        if (arteCulturaETurismoTimeout) {
            clearTimeout(arteCulturaETurismoTimeout);
        }
        if (serviziCommercialiTimeout) {
            clearTimeout(serviziCommercialiTimeout);
        }
    });

    const openEvento = (evento) => {
        $EVENTO_MODAL_PROPS.evento = evento;
        $EVENTO_MODAL_PROPS.comune = comune;
        EVENTO_MODAL_OPEN.set(true);
    }

    function openInGoogleMaps() {
        const searchQuery = comune.indirizzo ? comune.indirizzo : comune.nome + ', Italy';
        const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(searchQuery)}`;
        window.open(url, '_blank');
    }

    function mailto(email) {
        window.open(`mailto:${email}`);
    }

    const disclaimerAlboPretorio = 'L\'applicazione non è affiliata, associata o ufficialmente connessa con alcun comune del Salento o con altri enti governativi. Tutte le informazioni mostrate sono aggregate dai portali web istituzionali ufficiali dei rispettivi comuni. Per i contenuti ufficiali fare riferimento al sito ufficiale del comune: ';
    const disclaimerPrenotazioni = 'L\'applicazione non è affiliata, associata o ufficialmente connessa con alcun comune del Salento o con altri enti governativi. Per la prenotazione ti stiamo reindirizzando al sito ufficiale del comune: ';

    const openLink = async (link, disclaimer) => {
        try {
            if (!link.startsWith('http://') && !link.startsWith('https://')) {
                link = 'https://' + link;
            }
            if (disclaimer) {
                const alert = await alertController.create({
                    header: 'Disclaimer',
                    message: disclaimer + link,
                    buttons: [
                        {
                            text: 'Ok, ho capito',
                            handler: async () => {
                                await Browser.open({ url: link });
                            }
                        }
                    ]
                });
                await alert.present();
            } else
                await Browser.open({ url: link });
        } catch (error) {
            const toast = await toastController.create({
                message: 'Errore nell\'apertura del link',
                duration: 2000,
                position: 'bottom',
                color: 'danger'
            });
            await toast.present();
        }
    }
</script>

<ion-grid class="ion-padding main-grid">
    <!-- TITOLO -->
    <ion-row class="section">
        <ion-col size={12} class="comune-header">
            <ion-text class="h1">{comune.nome}</ion-text>
            <WeatherWidget location={comune.nome} apiKey={WEATHER_API_KEY} />
        </ion-col>
        <ion-col size={12}>
            {#if latitude && longitude && mapReady && mapOptions}
                <div
                    class="map-container"
                    on:click={openInGoogleMaps}
                    role="button"
                    tabindex="0"
                >
                    <Map
                        bind:this={mapElement}
                        options={mapOptions}
                    >
                        <TileLayer
                            url={'https://tile.openstreetmap.org/{z}/{x}/{y}.png'}
                            options={{
                                maxZoom: 19,
                                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                            }}
                        />
                        <Marker latLng={[latitude, longitude]}>
                            <Popup options={{ content: comune.nome }} />
                        </Marker>
                    </Map>
                </div>
            {:else}
                <div class="map-loading">
                    <ion-spinner name="crescent"></ion-spinner>
                </div>
            {/if}
        </ion-col>
    </ion-row>

    <!-- GIUNTA COMUNALE -->
    <ion-row class="section">
        <ion-col size={12}>
            <ion-text class="h2">{$t('municipal_council')}</ion-text>
        </ion-col>
        <ion-col size={12}>
            {#if membriGiuntaComunale.length > 0}
                <Splide aria-label={$t('municipal_council')} class="padding" options={sliderOptionsGiunta}>
                    {#each membriGiuntaComunale as membro}
                        <SplideSlide>
                            <ion-card class="giunta-comunale" on:click={() => {$MEMBRO_GIUNTA_MODAL_PROPS.membro = membro; $MEMBRO_GIUNTA_MODAL_PROPS.comune = comune; MEMBRO_GIUNTA_MODAL_OPEN.set(true);}}>
                                <div class="img-container">
                                    <ion-img
                                        loading="lazy"
                                        src={membro.foto || 'placeholder.jpg'}
                                        alt="{$t('photo_of')} {membro.cognome}"
                                        on:ionError={(e) => {
                                            e.target.src = 'placeholder.jpg';
                                        }}
                                    />
                                </div>
                                <div class="text-container">
                                    <ion-text style:font-size="large">{membro.nome} {membro.cognome}</ion-text>
                                    <ion-text>{parseMultiLanguageContent(membro.incarico)}</ion-text>
                                </div>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                </Splide>
            {:else}
                <Splide aria-label={$t('municipal_council')} class="padding" options={sliderOptionsGiunta}>
                    {#each Array(3) as _}
                        <SplideSlide>
                            <ion-card class="giunta-comunale">
                                <div class="img-container">
                                    <ion-skeleton-text animated style="width: 100%; height: 100%;"></ion-skeleton-text>
                                </div>
                                <div class="text-container">
                                    <ion-skeleton-text animated style="width: 80%;"></ion-skeleton-text>
                                    <ion-skeleton-text animated style="width: 60%;"></ion-skeleton-text>
                                </div>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                </Splide>
            {/if}
        </ion-col>

    </ion-row>

    <!-- BACHECA -->
    <ion-row class="section">
        <ion-col size={12}>
            <ion-text class="h2">{$t('bulletin_board')}</ion-text>
        </ion-col>
        <!-- TELEFONI COMUNALI -->
        {#if comune.telefono}
            <ion-col size={12}>
                <ion-text style:font-size="large">{$t('municipal_switchboard')}: <a href={'tel:+39 '+comune.telefono}>+39 {comune.telefono}</a></ion-text>
            </ion-col>
        {/if}
        {#if comune.telefono_vigili}
            <ion-col size={12}>
                <ion-text style:font-size="large">{$t('municipal_police')}: <a href={'tel:+39 '+comune.telefono_vigili}>+39 {comune.telefono_vigili}</a></ion-text>
            </ion-col>
        {/if}
        {#if comune.email}
            <ion-col size={6}>
                <ion-button expand="block" size="small" style:margin-top="4px" style:height="40px" on:click={() => mailto(comune.email)}>
                    <ion-text>{$t('email')}</ion-text>
                </ion-button>
            </ion-col>
        {/if}
        {#if comune.pec}
            <ion-col size={6}>
                <ion-button expand="block" size="small" style:margin-top="4px" style:height="40px" on:click={() => mailto(comune.pec)}>
                    <ion-text>PEC</ion-text>
                </ion-button>
            </ion-col>
        {/if}
        {#if !comune.convenzionato}
            <ion-col size={12}>
                <ion-card class="bacheca-card">
                        <Icon icon="ion:information-circle" width={35} color="var(--ion-color-primary)"/>
                        <div class="text">
                            <ion-text class="titolo">{$t('not_partner')}</ion-text>
                        </div>
                    </ion-card>
            </ion-col>
        {:else}
            {#if comune.link_albo_pretorio}
                <ion-col size="12">
                    <ion-card class="bacheca-card" on:click={() => openLink(comune.link_albo_pretorio, disclaimerAlboPretorio)}>
                        <Icon icon="ion:file-tray-full" width={35} color="var(--ion-color-primary)"/>
                        <div class="text">
                            <ion-text class="titolo">{$t('municipal_register')}</ion-text>
                            <ion-text class="descrizione">{$t('municipal_register_desc')}</ion-text>
                        </div>
                        <div class="icon">
                            <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                        </div>
                    </ion-card>
                </ion-col>
            {/if}
            {#if comune.link_prenotazioni}
                <ion-col size="12">
                    <ion-card class="bacheca-card" on:click={() => openLink(comune.link_prenotazioni, disclaimerPrenotazioni)}>
                        <Icon icon="ion:calendar" width={35} color="var(--ion-color-primary)"/>
                        <div class="text">
                            <ion-text class="titolo">{$t('appointments')}</ion-text>
                            <ion-text class="descrizione">{$t('appointments_desc')}</ion-text>
                        </div>
                        <div class="icon">
                            <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                        </div>
                    </ion-card>
                </ion-col>
            {/if}
            {#if comune.video?.length > 0}
                <ion-col size="12">
                    <ion-card class="bacheca-card" on:click={() => navController.push(VideoComune, {comune})}>
                        <Icon icon="ion:videocam" width={35} color="var(--ion-color-primary)"/>
                        <div class="text">
                            <ion-text class="titolo">{$t('videos')}</ion-text>
                            <ion-text class="descrizione">{$t('municipality_videos_desc')}</ion-text>
                        </div>
                        <div class="icon">
                            <Icon icon="ion:open-outline" width={25} color="var(--ion-color-primary)"/>
                        </div>
                    </ion-card>
                </ion-col>
            {/if}
        {/if}
    </ion-row>

    <!-- EMERGENZE -->
    <ion-row class="section">
        <ion-col size={12}>
            <ion-text class="h2">{$t('emergencies')}</ion-text>
        </ion-col>
        <ion-col size={12}>
            {#if emergenze.length > 0}
                {#each emergenze as categoria}
                    <ion-chip style:border-radius="50%" style:padding="8px" on:click={() => navController.push(Categoria, {categoria, comune})}>
                        <Icon icon={categoria.icona} width={35}/>
                    </ion-chip>
                {/each}
            {:else if emergenzeLoading}
                {#each Array(4) as _}
                    <ion-chip style:border-radius="50%" style:padding="8px">
                        <ion-skeleton-text animated style="width: 50px; height: 50px; border-radius: 50%;"></ion-skeleton-text>
                    </ion-chip>
                {/each}
            {:else}
                <ion-text>{$t('no_service_available')}</ion-text>
            {/if}
        </ion-col>
    </ion-row>

    <!-- SERVIZI ESSENZIALI -->
    <ion-row class="section">
        <ion-col size={12}>
            <div style:display="flex" style:justify-content="space-between" style:align-items="center">
                <ion-text class="h2">{$t('essential_services')}</ion-text>
                {#if serviziEssenziali.length > 0}
                    <ion-chip style:background="var(--ion-color-primary)" style:color="var(--ion-color-primary-contrast)" on:click={() => navController.push(ListaCategorie, {tipoCategoria:'Servizi essenziali', comune})}>
                        <ion-text>{$t('view_all')}</ion-text>
                        <Icon icon="material-symbols:arrow-outward-rounded" width={20} color="var(--ion-color-primary-contrast)"/>
                    </ion-chip>
                {/if}
            </div>
        </ion-col>
        <ion-col size={12}>
            <Splide aria-label={$t('essential_services')} class="padding" options={sliderOptionsCategorie}>
                {#if serviziEssenziali.length > 0}
                    {#each serviziEssenziali as categoria}
                        <SplideSlide>
                            <ion-card class="servizio-essenziale" on:click={() => navController.push(Categoria, {categoria, comune})}>
                                <div class="icon-container">
                                    <Icon icon={categoria.icona} width={35} color="black"/>
                                </div>
                                <div class="text">
                                    <ion-text class="titolo">{parseMultiLanguageContent(categoria.nome)}</ion-text>
                                </div>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                {:else if serviziEssenzialiLoading}
                    {#each Array(4) as _}
                        <SplideSlide>
                            <ion-card class="servizio-essenziale">
                                <ion-skeleton-text animated style="width: 100%; height: 100%;"/>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                {:else}
                    <ion-text>{$t('no_service_available')}</ion-text>
                {/if}
            </Splide>
        </ion-col>
    </ion-row>

    <!-- EVENTI -->
    <ion-row class="section">
        <ion-col size={12}>
            <div style:display="flex" style:justify-content="space-between" style:align-items="center">
                <ion-text class="h2">{$t('events')}</ion-text>
                {#if eventi.length > 0}
                    <ion-chip style:background="var(--ion-color-primary)" style:color="var(--ion-color-primary-contrast)" on:click={() => navController.push(Eventi, {comune})}>
                        <ion-text>{$t('view_all')}</ion-text>
                        <Icon icon="material-symbols:arrow-outward-rounded" width={20} color="var(--ion-color-primary-contrast)"/>
                    </ion-chip>
                {/if}
            </div>
        </ion-col>
        <ion-col size={12}>
            {#if allEventi.length === 0 && eventi.length === 0}
                <ion-text>{$t('no_events_scheduled')}</ion-text>
            {:else if eventi.length > 0}
                <Splide aria-label={$t('events')} class="padding" options={sliderOptionsEventi}>
                    {#each eventi as evento}
                        <SplideSlide>
                            <ion-card class="evento" on:click={() => openEvento(evento)}>
                                <div class="date-container">
                                    {#if evento.anteprima}
                                        <ion-img src={evento.anteprima} alt={$t('cover_image')}/>
                                    {/if}
                                    <ion-text class="day">{new Date(evento.data).getDate()}</ion-text>
                                    <ion-text class="month">{mesiMap[new Date(evento.data).getMonth() + 1]}</ion-text>
                                    <ion-text class="year">{new Date(evento.data).getFullYear()}</ion-text>
                                </div>
                                <div class="text-container">
                                    <ion-text class="titolo">{evento.titolo}</ion-text>
                                    <ion-text class="descrizione">{parseMultiLanguageContent(evento.descrizione)}</ion-text>
                                    <ion-text class="luogo-e-ora">{evento.luogo} - {evento.in_corso ? $t('in_progress') : evento.ora_inizio.slice(0, -3)}</ion-text>
                                </div>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                </Splide>
            {:else}
                <Splide aria-label={$t('events')} class="padding" options={sliderOptionsEventi}>
                    {#each Array(3) as _}
                        <SplideSlide>
                            <ion-card class="evento">
                                <div class="date-container">
                                    <ion-skeleton-text animated style="width: 60%; height: 30px;"></ion-skeleton-text>
                                    <ion-skeleton-text animated style="width: 40%; height: 20px;"></ion-skeleton-text>
                                </div>
                                <div class="text-container">
                                    <ion-skeleton-text animated style="width: 80%;"></ion-skeleton-text>
                                    <ion-skeleton-text animated style="width: 60%;"></ion-skeleton-text>
                                    <ion-skeleton-text animated style="width: 40%;"></ion-skeleton-text>
                                </div>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                </Splide>
            {/if}
        </ion-col>
    </ion-row>

    <!-- ARTE, CULTURA E TURISMO -->
    <ion-row class="section">
        <ion-col size={12}>
            <div style:display="flex" style:justify-content="space-between" style:align-items="center">
                <ion-text class="h2">{$t('art_culture_tourism')}</ion-text>
                {#if arteCulturaETurismo.length > 0}
                    <ion-chip style:background="var(--ion-color-primary)" style:color="var(--ion-color-primary-contrast)" on:click={() => navController.push(ListaCategorie, {tipoCategoria:'Arte, cultura e turismo', comune})}>
                    <ion-text>{$t('view_all')}</ion-text>
                        <Icon icon="material-symbols:arrow-outward-rounded" width={20} color="var(--ion-color-primary-contrast)"/>
                    </ion-chip>
                {/if}
            </div>
        </ion-col>
        <ion-col size={12}>
            <Splide aria-label={$t('art_culture_tourism')} class="padding" options={sliderOptionsCategorie}>
                {#if arteCulturaETurismo.length > 0}
                    {#each arteCulturaETurismo as categoria}
                        <SplideSlide>
                            <ion-card class="evidenza" on:click={() => navController.push(Categoria, {categoria, comune})}>
                                <div class="button">
                                    <ion-img
                                    loading="lazy"
                                    src={categoria.anteprima || 'placeholder.jpg'}
                                    alt={categoria.nome}
                                    on:ionError={(e) => {
                                        e.target.src = 'placeholder.jpg';
                                    }}
                                    />
                                </div>
                                <div class="text">
                                    <ion-text class="titolo">{parseMultiLanguageContent(categoria.nome)}</ion-text>
                                </div>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                {:else if arteCulturaETurismoLoading}
                    {#each Array(4) as _}
                        <SplideSlide>
                            <ion-card class="servizio-essenziale">
                                <ion-skeleton-text animated style="width: 100%; height: 100%;"/>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                {:else}
                    <ion-text>{$t('no_service_available')}</ion-text>
                {/if}
            </Splide>
        </ion-col>
    </ion-row>

    <!-- Servizi commerciali -->
    <ion-row class="section">
        <ion-col size={12}>
            <div style:display="flex" style:justify-content="space-between" style:align-items="center">
                <ion-text class="h2">{$t('commercial_services')}</ion-text>
                {#if serviziCommerciali.length > 0}
                    <ion-chip style:background="var(--ion-color-primary)" style:color="var(--ion-color-primary-contrast)" on:click={() => navController.push(ListaCategorie, {tipoCategoria:'Servizi commerciali', comune})}>
                        <ion-text>{$t('view_all')}</ion-text>
                        <Icon icon="material-symbols:arrow-outward-rounded" width={20} color="var(--ion-color-primary-contrast)"/>
                    </ion-chip>
                {/if}
            </div>
        </ion-col>
        <ion-col size={12}>
            <Splide aria-label={$t('commercial_services')} class="padding" options={sliderOptionsCategorie}>
                {#if serviziCommerciali.length > 0}
                    {#each serviziCommerciali as categoria}
                        <SplideSlide>
                            <ion-card class="evidenza" on:click={() => navController.push(Categoria, {categoria, comune})}>
                                <div class="button">
                                    <ion-img
                                    loading="lazy"
                                    src={categoria.anteprima || 'placeholder.jpg'}
                                    alt={categoria.nome}
                                    on:ionError={(e) => {
                                        e.target.src = 'placeholder.jpg';
                                    }}
                                    />
                                </div>
                                <div class="text">
                                    <ion-text class="titolo">{parseMultiLanguageContent(categoria.nome)}</ion-text>
                                </div>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                {:else if serviziCommercialiLoading}
                    {#each Array(4) as _}
                        <SplideSlide>
                            <ion-card class="servizio-essenziale">
                                <ion-skeleton-text animated style="width: 100%; height: 100%;"/>
                            </ion-card>
                        </SplideSlide>
                    {/each}
                {:else}
                    <ion-text>{$t('no_service_available')}</ion-text>
                {/if}
            </Splide>
        </ion-col>
    </ion-row>
</ion-grid>

<style>
    .comune-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .map-container {
        width: 100%;
        height: 150px;
        border-radius: 25px;
        box-shadow: 8px 8px 20px gray;
        overflow: hidden;
        position: relative;
        z-index: 1;
        cursor: pointer;
        background-color: white;
    }

    .map-loading {
        width: 100%;
        height: 150px;
        border-radius: 25px;
        box-shadow: 8px 8px 20px gray;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;
    }

    :global(.leaflet-container) {
        width: 100% !important;
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: white !important;
    }

    :global(.leaflet-tile-container) {
        height: 100% !important;
        width: 100% !important;
    }

    :global(.leaflet-tile) {
        visibility: inherit !important;
    }

    ion-card.giunta-comunale {
        width: 200px;
        height: 100px;
        margin: 0px;
        margin-bottom: 8px;
        margin-right: 8px;
        display: flex;
        align-items: center;
    }

    ion-card.giunta-comunale > div.img-container {
        width: 35%;
        height: 100%;
    }

    ion-card.giunta-comunale > div.img-container > ion-img {
        height: 100%;
        object-fit: cover;
    }

    ion-card.giunta-comunale > div.img-container > ion-img::part(image) {
        border-radius: 25px;
    }

    ion-card.giunta-comunale > div.text-container {
        width: 65%;
        height: 100%;
        padding: 10px 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;
        color: black;
    }

    ion-card.evento {
        width: 320px;
        height: 130px;
        margin: 0px;
        margin-bottom: 8px;
        margin-right: 8px;
        transition: all .5s ease-in-out;

        display: flex;
        align-items: center;
    }

    ion-card.evento > div.date-container {
        position: relative;
        height: 100%;
        width: 20%;
        min-width: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: white;
        padding: 0px 8px;
        border-radius: 25px;
        background: var(--ion-color-primary);
    }

    ion-card.evento > div.date-container > ion-img {
        position: absolute;
        object-fit: cover;
        top: 0;
        left: 0;
        height: 100%;
        max-width: 100%;
        filter: opacity(0.5);
    }

    ion-card.evento > div.date-container > ion-img::part(image) {
        border-radius: 25px !important;
    }
    ion-card.evento > div.date-container > ion-text.day {
        z-index: 2;
        font-size: 32;
        font-weight: bold;
    }

    ion-card.evento > div.date-container > ion-text.month {
        z-index: 2;
        font-size: 18;
    }

    ion-card.evento > div.date-container > ion-text.year {
        z-index: 2;
    }

    ion-card.evento > div.text-container {
        width: 80%;
        height: 100%;
        padding: 16px 8px;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        gap: 2px;
        color: black;
    }

    ion-card.evento > div.text-container > ion-text.titolo {
        font-size: 20;
        font-weight: bold;
    }

    ion-card.evento > div.text-container > ion-text.descrizione {
        font-size: medium;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }

    ion-card.evento > div.text-container > ion-text.luogo-e-ora {
        font-size: medium;
        color: gray;
    }

    ion-card.evidenza {
        aspect-ratio: 1;
        width: 140px;
        height: 140px;
        margin: 0px;
        margin-right: 8px;
        margin-bottom: 2px;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
    }

    ion-card.evidenza > div.button {
        flex: 0.9;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: stretch;
        justify-content: center;
        overflow: hidden;
        position: relative;
    }

    ion-card.evidenza > div.button > ion-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza > div.button > ion-img::part(image) {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    ion-card.evidenza .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 8px 12px;
        flex: 0.3;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    ion-card.evidenza .text .titolo {
        font-size: medium;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }

    ion-card.servizio-essenziale {
        width: 100px;
        height: 100px;
        margin: 0px;
        margin-right: 8px;
        margin-bottom: 2px;
        transition: all .3s ease-in-out;
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 16px;
    }

    ion-card.servizio-essenziale > div.icon-container {
        flex: 0.7;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: black;
        background: var(--ion-color-light);
    }

    ion-card.servizio-essenziale .text {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 8px;
        flex: 0.3;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    ion-card.servizio-essenziale .text .titolo {
        font-size: small;
        font-weight: 600;
        color: var(--ion-color-dark);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        line-height: 1.2;
    }

    ion-card.bacheca-card {
        height: auto;
        min-height: 80px;
        margin: 0px;
        margin-bottom: 2px;
        transition: all .5s ease-in-out;
        display: flex;
        align-items: center;
        background: white;
        border-radius: 25px;
        padding: 0 16px;
    }

    ion-card.bacheca-card .icon {
        margin-right: 12px;
        flex-shrink: 0;
    }

    ion-card.bacheca-card .text {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        padding: 16px;
        min-width: 0;
    }

    ion-card.bacheca-card .text .titolo {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--ion-color-dark);
        white-space: normal;
        word-break: break-word;
        line-height: 1.2;
    }

    ion-card.bacheca-card .text .descrizione {
        font-size: 0.9rem;
        color: var(--ion-color-medium);
        white-space: normal;
        word-break: break-word;
        line-height: 1.2;
    }
</style>
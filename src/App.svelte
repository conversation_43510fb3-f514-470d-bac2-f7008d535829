<script>
    /* Ionic */
    import { App as CapacitorApp } from '@capacitor/app';
    import { setupIonicBase, navController } from 'ionic-svelte';
    import Home from '@pages/Home.svelte';
    import AccountModal from '@components/modals/AccountModal.svelte';
    import MembroGiuntaModal from '@components/modals/MembroGiuntaModal.svelte';
    import EventoModal from '@components/modals/EventoModal.svelte';
    import ArtistaModal from '@components/modals/ArtistaModal.svelte';
    import ContactUsModal from '@components/modals/ContactUsModal.svelte';
    import WorkWithUsModal from '@components/modals/WorkWithUsModal.svelte';
    import PreferencesModal from '@components/modals/PreferencesModal.svelte';
    import PrivacyPolicyModal from '@components/modals/PrivacyPolicyModal.svelte';
    import TermsAndConditionsModal from '@components/modals/TermsAndConditionsModal.svelte';
    import AboutUsModal from '@components/modals/AboutUsModal.svelte';
    import AddActivityModal from '@components/modals/AddActivityModal.svelte';
    import IonNav from 'ionic-svelte/components/IonNav.svelte';
    import HomeFooter from '@components/home/<USER>';
    import 'ionic-svelte/components/all';    
    import { get } from 'svelte/store';

    /* Custom */
    import { 
        ACTIVE_TAB,
        ACCOUNT_MODAL_OPEN,
        MEMBRO_GIUNTA_MODAL_OPEN,
        EVENTO_MODAL_OPEN,
        ARTISTA_MODAL_OPEN,
        CONTACT_US_MODAL_OPEN,
        WORK_WITH_US_MODAL_OPEN,
        PREFERENCES_MODAL_OPEN,
        PRIVACY_POLICY_MODAL_OPEN,
        TERMS_AND_CONDITIONS_MODAL_OPEN,
        ABOUT_US_MODAL_OPEN,
        ADD_ACTIVITY_MODAL_OPEN
    } from '@store';

    /* Theme variables */
    import './theme/variables.css';
    
    setupIonicBase();

    // Function to check if any modal is open and close it
    function closeOpenModals() {
        let modalWasClosed = false;
        
        const modalStores = [
            ACCOUNT_MODAL_OPEN,
            MEMBRO_GIUNTA_MODAL_OPEN,
            EVENTO_MODAL_OPEN,
            ARTISTA_MODAL_OPEN,
            CONTACT_US_MODAL_OPEN,
            WORK_WITH_US_MODAL_OPEN,
            PREFERENCES_MODAL_OPEN,
            PRIVACY_POLICY_MODAL_OPEN,
            TERMS_AND_CONDITIONS_MODAL_OPEN,
            ABOUT_US_MODAL_OPEN,
            ADD_ACTIVITY_MODAL_OPEN
        ];

        modalStores.forEach(modalStore => {
            const isOpen = get(modalStore);
            
            if (isOpen) {
                modalStore.set(false);
                modalWasClosed = true;
            }
        });

        return modalWasClosed;
    }

    document.addEventListener('ionBackButton', async (ev) => {
        // First check if any modal is open
        if (closeOpenModals()) {
            return;
        }
        
        // If no modal was open, handle normal back navigation
        if (await navController.canGoBack()) {
            navController.pop();
        } else if ($ACTIVE_TAB !== 'Home') {
            // If we can't go back and we're not in the Home tab,
            // navigate to Home tab
            ACTIVE_TAB.set('Home');
            navController.setRoot(Home);
        } else {
            // If we're already in the Home tab, exit the app
            CapacitorApp.exitApp();
        }
    });

    // Handle tab changes
    function handleTabChange(event) {
        const tabName = event.detail.tabName;
        // Reset navigation stack when switching tabs
        navController.setRoot(Home);
        ACTIVE_TAB.set(tabName);
    }
</script>

<ion-app>
    <ion-content>
        <IonNav root={Home} swipeGesture={true} animated={false} />
    </ion-content>

    <!-- Bottom Tab Bar - Always visible -->
    <HomeFooter on:tabChange={handleTabChange} />

    <!-- MODALS -->
    <AccountModal />
    <MembroGiuntaModal />
    <EventoModal />
    <ArtistaModal />
    <ContactUsModal />
    <WorkWithUsModal />
    <PreferencesModal />
    <PrivacyPolicyModal />
    <TermsAndConditionsModal />
    <AboutUsModal />
    <AddActivityModal />
</ion-app>

<style>
    /* Ensure content doesn't overlap with footer */
    :global(ion-content) {
        --padding-bottom: 56px; /* Height of the tab bar */
    }
</style>

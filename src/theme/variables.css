/* Ionic Variables and Theming. For more info, please see:
http://ionicframework.com/docs/theming/ */

/** Ionic CSS Variables **/
:root {
	/*  tailored elements */
	--ion-background-color: #F9FAFB;

	--ion-toolbar-background: transparent;

	/* Gradients */
	--ion-color-grad-primary-secondary: linear-gradient(90deg, rgb(22, 50, 91) 0%, rgb(0, 116, 217) 100%);
	--ion-color-grad-secondary-tertiary: linear-gradient(90deg, rgb(0, 116, 217) 0%, rgb(32, 178, 170) 100%);
	--ion-color-grad-primary-secondary-tertiary: linear-gradient(90deg, rgb(22, 50, 91) 0%, rgb(0, 116, 217) 50%, rgb(32, 178, 170) 100%);

	/** bg **/
	--ion-color-bg:  #16325b;
	--ion-color-bg-rgb: 22, 50, 91;
	--ion-color-bg-contrast: #ffffff;
	--ion-color-bg-contrast-rgb: 255, 255, 255;
	--ion-color-bg-shade: #0c1c33;
	--ion-color-bg-tint: #1e4177;
	--ion-color-bg-transparent: rgba(22, 50, 91, 0.08);
	
	
	/** primary **/
	--ion-color-primary: #D1172E;
	--ion-color-primary-rgb: 209, 23, 46;
	--ion-color-primary-contrast: #ffffff;
	--ion-color-primary-contrast-rgb: 255, 255, 255;
	--ion-color-primary-shade: #850606;
	--ion-color-primary-tint: #d62e43;
	--ion-color-primary-transparent: rgba(209, 23, 46, 0.08);
	
	
	/** secondary **/
	--ion-color-secondary: #20b2aa;
	--ion-color-secondary-rgb: 32, 178, 170;
	--ion-color-secondary-contrast: #ffffff;
	--ion-color-secondary-contrast-rgb: 255, 255, 255;
	--ion-color-secondary-shade: #1a928c;
	--ion-color-secondary-tint: #36cec6;
	--ion-color-secondary-transparent: rgba(32, 178, 170, 0.1);
	--ion-color-secondary-card-bg: rgba(32, 178, 170, 0.8);

	/** success **/
	--ion-color-success: #2dd36f;
	--ion-color-success-rgb: 45, 211, 111;
	--ion-color-success-contrast: #ffffff;
	--ion-color-success-contrast-rgb: 255, 255, 255;
	--ion-color-success-shade: #28ba62;
	--ion-color-success-tint: #42d77d;

	/** warning **/
	--ion-color-warning: #c7c10b;
	--ion-color-warning-rgb: 255, 196, 9;
	--ion-color-warning-contrast: #ffffff;
	--ion-color-warning-contrast-rgb: 0, 0, 0;
	--ion-color-warning-shade: #dad426;
	--ion-color-warning-tint: #f7f14d;

	/** danger **/
	--ion-color-danger: #eb445a;
	--ion-color-danger-rgb: 235, 68, 90;
	--ion-color-danger-contrast: #ffffff;
	--ion-color-danger-contrast-rgb: 255, 255, 255;
	--ion-color-danger-shade: #cf3c4f;
	--ion-color-danger-tint: #ed576b;

	/** dark **/
	--ion-color-dark: #222428;
	--ion-color-dark-rgb: 34, 36, 40;
	--ion-color-dark-contrast: #ffffff;
	--ion-color-dark-contrast-rgb: 255, 255, 255;
	--ion-color-dark-shade: #1e2023;
	--ion-color-dark-tint: #383a3e;

	/** medium **/
	--ion-color-medium: #666;
	--ion-color-medium-rgb: 146, 148, 156;
	--ion-color-medium-contrast: #ffffff;
	--ion-color-medium-contrast-rgb: 255, 255, 255;
	--ion-color-medium-shade: #808289;
	--ion-color-medium-tint: #9d9fa6;

	/** light **/
	--ion-color-light: #e7e7e9;
	--ion-color-light-rgb: 244, 245, 248;
	--ion-color-light-contrast: #000000;
	--ion-color-light-contrast-rgb: 0, 0, 0;
	--ion-color-light-shade: #d7d8da;
	--ion-color-light-tint: #f5f6f9;
}
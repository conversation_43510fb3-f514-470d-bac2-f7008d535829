<script>
    import MembroGiuntaContent from '@components/membro-giunta/MembroGiuntaContent.svelte';
    import HeaderContent from '@components/HeaderContent.svelte';
    import IonPage from 'ionic-svelte/components/IonPage.svelte';
    
    export let comune;
    export let membro;
</script>

<IonPage>
    <!-- HEADER -->
    <ion-header>
        <HeaderContent canGoBack/>
    </ion-header>

    <!-- CONTENT -->
    <ion-content>
        <MembroGiuntaContent {comune} {membro}/>
    </ion-content>
</IonPage>
<script>
    import EventiContent from '@components/eventi/EventiContent.svelte';
    import HeaderContent from '@components/HeaderContent.svelte';
    import IonPage from 'ionic-svelte/components/IonPage.svelte';
    
    export let comune;
</script>

<IonPage>
    <!-- HEADER -->
    <ion-header>
        <HeaderContent canGoBack/>
    </ion-header>

    <!-- CONTENT -->
    <ion-content>
        <EventiContent {comune}/>
    </ion-content>
</IonPage>
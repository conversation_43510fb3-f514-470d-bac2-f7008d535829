<script>
    import { ACTIVE_TAB } from '@store';
    import IonPage from 'ionic-svelte/components/IonPage.svelte';
    import HeaderContent from '@components/HeaderContent.svelte';
    import HomeContent from '@components/home/<USER>';
    import HomeFooter from '@components/home/<USER>';   
</script>

<IonPage>
    <!-- HEADER -->
    <ion-header>
        <HeaderContent canGoBack={false}/>
    </ion-header>

    <!-- CONTENT -->
    <ion-content>
        <HomeContent />
    </ion-content>

    <ion-footer>
        <HomeFooter on:tabChange={(e) => ACTIVE_TAB.set(e.detail.tabName)}/>
    </ion-footer>
</IonPage>
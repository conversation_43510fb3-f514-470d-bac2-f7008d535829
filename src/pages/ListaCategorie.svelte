<script>
    import ListaCategorieContent from '@components/lista-categorie/ListaCategorieContent.svelte';
    import HeaderContent from '@components/HeaderContent.svelte';
    import IonPage from 'ionic-svelte/components/IonPage.svelte';
    
    export let tipoCategoria;
    export let comune;
</script>

<IonPage>
    <!-- HEADER -->
    <ion-header>
        <HeaderContent canGoBack/>
    </ion-header>

    <!-- CONTENT -->
    <ion-content>
        <ListaCategorieContent {tipoCategoria} {comune}/>
    </ion-content>
</IonPage>
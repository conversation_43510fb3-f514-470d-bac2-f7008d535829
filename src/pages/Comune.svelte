<script>
    import IonPage from 'ionic-svelte/components/IonPage.svelte';
    import HeaderContent from '@components/HeaderContent.svelte';
    import ComuneContent from '@components/comune/ComuneContent.svelte';
    import { selectAnnunci } from "@lib/supabase/";
    import Icon from '@iconify/svelte';
    import { onMount } from "svelte";

    export let comune;
    let annunci = [];

    onMount(async () => {
        annunci = await selectAnnunci(comune.id) || [];
    })
</script>

<IonPage>
        <!-- HEADER -->
        <ion-header>
            <HeaderContent canGoBack/>
        </ion-header>

        <!-- BANNER -->
         {#if annunci.length > 0}   
            <div class="banner" >
                <div class="scrolling-text">
                    {#each annunci as annuncio}
                        <div class="announcement-message-container">
                            <Icon icon="ion:warning" color="white" width={25}/>
                            <ion-text class="announcement-message">{annuncio.messaggio}</ion-text>
                        </div>
                    {/each}
                </div>
            </div>
         {/if}
        
        <!-- CONTENT -->
        <ion-content class:no-after={annunci.length > 0}>
            <ComuneContent {comune}/>
        </ion-content>
</IonPage>

<style>
    .no-after::after {
        display: none;
    }

    .banner {
        height: 50px;
        background: var(--ion-color-danger);
        overflow: hidden;
        display: flex;
        align-items: center;
    }

    .scrolling-text {
        flex-grow: 1;
        display: flex;
        align-items: center;
        gap: 30px;

        animation: scroll-left 10s linear infinite;
    }

    .announcement-message-container {
        flex-grow: 1;
        display: flex;
        align-items: flex-end;
        gap: 10px;
    }

    .announcement-message {
        font-size: 16px;
        font-weight: bold;
        color: white;
    }

    /* Setting the Animation using Keyframes */
    @keyframes scroll-left {
        0% {
            transform: translateX(100%);
        }

        100% {
            transform: translateX(-100%);
        }
    }
</style>
import { writable } from 'svelte/store';

// Helper function to safely access localStorage (handles cases where it might be unavailable)
const getLocalStorage = (key, defaultValue) => {
    try {
        const storedValue = localStorage.getItem(key);
        return storedValue ? JSON.parse(storedValue) : defaultValue;
    } catch (error) {
        console.error('Error accessing localStorage:', error);
        return defaultValue;
    }
};

// Helper function to safely set localStorage
const setLocalStorage = (key, value) => {
    try {
        localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
        console.error('Error setting localStorage:', error);
    }
};

// Create a writable store with localStorage persistence
const createPersistentStore = (key, initialValue) => {
    // Get the initial value from localStorage or use the provided default
    const storedValue = getLocalStorage(key, initialValue);
    
    // Create a writable store with the initial value
    const store = writable(storedValue);
    
    // Subscribe to changes and update localStorage
    store.subscribe(value => {
        setLocalStorage(key, value);
    });
    
    return store;
};

// User preferences store
export const userLanguage = createPersistentStore('il-salento-language', 'it');

// You can add more user preferences here as needed
export const notificationsEmail = createPersistentStore('il-salento-notifications-email', true);
export const notificationsPush = createPersistentStore('il-salento-notifications-push', false);

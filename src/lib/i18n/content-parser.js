import { getCurrentLanguage } from './index';

/**
 * Extracts the appropriate language content from a multi-language string
 * @param {string} content - String with language markers like [IT], [EN]
 * @returns {string} The content in the appropriate language
 */
export function parseMultiLanguageContent(content) {
  if (!content) return '';
  
  // Get current language
  const currentLang = getCurrentLanguage().toUpperCase() || 'IT';
  
  // Regular expression to match language sections
  const langSections = {};
  const regex = /\[(.*?)\]([\s\S]*?)(?=\[[A-Z]{2}\]|$)/g;
  let match;
  
  while ((match = regex.exec(content)) !== null) {
    const lang = match[1].trim();
    const text = match[2].trim();
    langSections[lang] = text;
  }
  
  // Return the content in the current language or fallback
  return langSections[currentLang] || content;
}

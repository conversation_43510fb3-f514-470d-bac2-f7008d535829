import { writable, derived } from 'svelte/store';
import { userLanguage } from '../stores/preferences';

// Import all language files
import it from './it';
import en from './en';

// Create a dictionary of all available translations
const translations = {
    it,
    en
};

// Create a derived store that updates when the language changes
export const locale = writable('it');

// Subscribe to userLanguage changes and update locale
userLanguage.subscribe(lang => {
    // Ensure the language is supported, default to Italian if not
    if (translations[lang]) {
        locale.set(lang);
    } else {
        console.warn(`Language ${lang} not supported, falling back to Italian`);
        locale.set('it');
    }
});

// Create a derived store for the translations
export const t = derived(locale, $locale => {
    return key => {
        // Get the translations for the current locale
        const translationObj = translations[$locale];
        
        // Check if the key exists in the translations
        if (translationObj && translationObj[key] !== undefined) {
            return translationObj[key];
        }
        
        // If the key doesn't exist in the current locale, try Italian as fallback
        if ($locale !== 'it' && translations.it && translations.it[key] !== undefined) {
            console.warn(`Translation key "${key}" missing in ${$locale}, using Italian fallback`);
            return translations.it[key];
        }
        
        // If all else fails, return the key itself
        console.warn(`Translation key "${key}" missing in all languages`);
        return key;
    };
});

/**
 * Get the current user language
 * @returns {string} The language code (e.g., 'it', 'en')
 */
export const getCurrentLanguage = () => {
    let lang;
    userLanguage.subscribe(value => {
        lang = value;
    })();
    return lang;
};

/**
 * Set the user language
 * @param {string} lang - The language code to set (e.g., 'it', 'en')
 */
export const setLanguage = (lang) => {
    // Check if the language is supported
    if (translations[lang]) {
        userLanguage.set(lang);
    } else {
        console.warn(`Language ${lang} not supported, not changing`);
    }
};

/**
 * Get all available languages
 * @returns {Object[]} Array of language objects with code and name
 */
export const getAvailableLanguages = () => {
    return [
        { code: 'it', name: 'Italiano' },
        { code: 'en', name: 'English' }
        // Add more languages here as they become available
    ];
};

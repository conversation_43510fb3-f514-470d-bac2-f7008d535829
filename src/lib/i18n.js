// Re-export everything from the i18n/index.js file
export * from './i18n/index';

// For backward compatibility
import { t } from './i18n/index';

/**
 * Translate a key
 * @param {string} key - The translation key
 * @param {Object} params - Parameters for the translation (for future use)
 * @returns {string} The translated text
 */
export const translate = (key, params = {}) => {
    return t.subscribe(translateFn => {
        return translateFn(key);
    })();
};

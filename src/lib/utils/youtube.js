/**
 * Extracts the YouTube video ID from various YouTube URL formats
 * @param {string} url - The YouTube URL
 * @returns {string|null} - The YouTube video ID or null if not found
 */
export const extractYoutubeVideoId = (url) => {
    if (!url) return null;
    
    // Ensure URL has protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
    }
    
    try {
        const urlObj = new URL(url);
        
        // Handle youtube.com URLs
        if (urlObj.hostname === 'www.youtube.com' || urlObj.hostname === 'youtube.com') {
            // Handle watch URLs
            if (urlObj.pathname === '/watch') {
                const videoId = urlObj.searchParams.get('v');
                return videoId;
            }
            
            // Handle embed URLs
            if (urlObj.pathname.startsWith('/embed/')) {
                return urlObj.pathname.split('/')[2];
            }
            
            // <PERSON>le shortened URLs
            if (urlObj.pathname.startsWith('/v/')) {
                return urlObj.pathname.split('/')[2];
            }
        }
        
        // Handle youtu.be URLs
        if (urlObj.hostname === 'youtu.be') {
            return urlObj.pathname.substring(1);
        }
        
        return null;
    } catch (error) {
        console.error('Error extracting YouTube video ID:', error);
        return null;
    }
};

/**
 * Checks if a URL is a valid YouTube URL
 * @param {string} url - The URL to check
 * @returns {boolean} - True if the URL is a valid YouTube URL
 */
export const isYoutubeUrl = (url) => {
    return extractYoutubeVideoId(url) !== null;
};

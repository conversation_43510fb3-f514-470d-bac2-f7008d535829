import { supabase } from './supabaseClient';

export const authService = {
    async signInWithGoogle(redirectTo) {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: redirectTo
            }
        });

        if (error) return { error };
        return { data };
    },
    
    async signOut() {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
    },

    onAuthStateChange(callback) {
        return supabase.auth.onAuthStateChange((event, session) => {
            callback(event, session);
        });
    },

    async getLoggedUser() {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        return session?.user;
    }
}; 
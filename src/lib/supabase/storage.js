import { supabase } from './supabaseClient';

export const getAnteprimaComune = async (nomeComune) => {
    try {
        nomeComune = nomeComune.normalize('NFD').replace(/[\u0300-\u036f]/g, "");
        const { data } = supabase.storage.from('comuni')
            .getPublicUrl(`${nomeComune}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getFotoMembroGiuntaComunale = async (nomeComune, membro) => {
    try {
        nomeComune = nomeComune.normalize('NFD').replace(/[\u0300-\u036f]/g, "");
        const { data } = supabase.storage.from('giunte_comunali')
            .getPublicUrl(`${nomeComune}/${membro.nome} ${membro.cognome}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}


export const getAnteprimaCategoria = async (nomeCategoria) => {
    try {
        nomeCategoria = nomeCategoria.normalize('NFD').replace(/[\u0300-\u036f]/g, "");
        const { data } = supabase.storage.from('categorie')
            .getPublicUrl(`${nomeCategoria}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getAnteprimaCategoriaArtisti = async (nomeCategoria) => {
    try {
        nomeCategoria = nomeCategoria.normalize('NFD').replace(/[\u0300-\u036f]/g, "");
        const { data } = supabase.storage.from('categorie_artisti')
            .getPublicUrl(`${nomeCategoria}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getAnteprimaCategoriaSalentoWorld = async (nomeCategoria) => {
    try {
        nomeCategoria = nomeCategoria.normalize('NFD').replace(/[\u0300-\u036f]/g, "");
        const { data } = supabase.storage.from('categorie')
            .getPublicUrl(`${nomeCategoria}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getAnteprimaServizio = async (nomeServizio) => {
    try {
        nomeServizio = nomeServizio.normalize('NFD').replace(/[\u0300-\u036f]/g, "");
        const { data } = supabase.storage.from('servizi')
            .getPublicUrl(`${nomeServizio}/${nomeServizio}-1.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getAnteprimaArtista = async (nomeArtista) => {
    try {
        const { data } = supabase.storage.from('artisti')
            .getPublicUrl(`${nomeArtista}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getImmaginiServizio = async (nomeServizio) => {
    try {
        let images = [];

        nomeServizio = nomeServizio.normalize('NFD').replace(/[\u0300-\u036f]/g, "");

        // Try to list files in the directory
        // According to Supabase docs, the path should NOT have a leading slash
        const { data: files, error } = await supabase.storage
            .from('servizi')
            .list(nomeServizio, {
                limit: 100,
                offset: 0,
                sortBy: { column: 'name', order: 'asc' }
            });

        if (error) {
            console.error('Error listing files:', error);
            // Fall back to just getting the first image
            const { data } = supabase.storage.from('servizi')
                .getPublicUrl(`${nomeServizio}/${nomeServizio}-1.jpg`);

            if (data && data.publicUrl) {
                images.push(data.publicUrl);
            }
            return images;
        }

        if (!files || files.length === 0) {
            // Fall back to just getting the first image
            const { data } = supabase.storage.from('servizi')
                .getPublicUrl(`${nomeServizio}/${nomeServizio}-1.jpg`);

            if (data && data.publicUrl) {
                images.push(data.publicUrl);
            }
            return images;
        }

        // Filter for jpg files and sort them by the number in the filename
        const imageFiles = files
            .filter(file => file.name.endsWith('.jpg'))
            .sort((a, b) => {
                try {
                    // Extract the number from the filename (e.g., "servizio-1.jpg" -> 1)
                    const numA = parseInt(a.name.split('-').pop().split('.')[0]);
                    const numB = parseInt(b.name.split('-').pop().split('.')[0]);
                    return numA - numB;
                } catch (e) {
                    console.error('Error sorting files:', e);
                    return 0;
                }
            });

        // Get public URLs for all found images
        for (const file of imageFiles) {
            const { data } = supabase.storage.from('servizi')
                .getPublicUrl(`${nomeServizio}/${file.name}`);

            if (data && data.publicUrl) {
                images.push(data.publicUrl);
            }
        }

        return images;
    } catch (error) {
        console.error('Error fetching images:', error);
        return [];
    }
}

export const getAnteprimaEvento = async (idEvento) => {
    try {
        const { data } = supabase.storage.from('eventi')
            .getPublicUrl(`${idEvento}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getAnteprimaInEvidenza = async (idInEvidenza) => {
    try {
        const { data } = supabase.storage.from('in_evidenza')
            .getPublicUrl(`${idInEvidenza}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}

export const getAnteprimaVideo = async (idVideo) => {
    try {
        const { data } = supabase.storage.from('video')
            .getPublicUrl(`${idVideo}.jpg`);

        return data.publicUrl;
    } catch (error) {
        return;
    }
}
export const uploadFotoRichiestaInserimentoAttivita = async (file, nomeAttivita) => {
    try {
        const fileName = `${nomeAttivita}-${Date.now()}.jpg`;
        const { error } = await supabase.storage
            .from('richieste')
            .upload(fileName, file, {
                cacheControl: '3600',
                upsert: false,
                contentType: 'image/jpeg'
            });

        if (error) throw error;
        return fileName;
    } catch (error) {
        console.error('Error uploading file:', error);
        throw error;
    }
};

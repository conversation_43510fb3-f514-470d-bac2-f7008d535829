// Weather API utility functions

// Get the WeatherAPI.com API key from environment variables
// This uses the VITE_WEATHER_API_KEY from .env.local
const WEATHER_API_KEY = import.meta.env.VITE_WEATHER_API_KEY;

/**
 * Fetches current weather data for a given location
 * @param {string} location - The location to get weather for (city name, coordinates, etc.)
 * @returns {Promise<Object>} - Weather data object or null if there's an error
 */
export const getCurrentWeather = async (location) => {
    try {
        // Add Italy to the location to improve accuracy for Italian locations
        const searchLocation = `${location}, Italy`;
        const response = await fetch(
            `https://api.weatherapi.com/v1/current.json?key=${WEATHER_API_KEY}&q=${encodeURIComponent(searchLocation)}&aqi=no`
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error?.message || 'Failed to fetch weather data');
        }

        return await response.json();
    } catch (error) {
        console.error('Weather API error:', error);
        return null;
    }
};

# Gestione e Rilascio App

## Gestione Dominio e Email

1. Acquistare dominio con email (es. [Aruba](https://hosting.aruba.it/email.aspx))
2. Creare caselle di posta elettronica su dominio (es. <EMAIL>)
3. Configurare server SMTP su [Supabase](https://supabase.com/docs/guides/auth/auth-smtp)

## Rilascio App su Google Play

1. Creare un account sviluppatore Google Play ([link](https://play.google.com/console/developers/create)) - 25€ una tantum
2. Rilasciare l'app su Google Play Console tramite [Android Studio](https://ionic.io/blog/building-and-releasing-your-capacitor-android-app)

## Rilascio App su Apple Store

1. Creare un account sviluppatore Apple Developer ([link](https://developer.apple.com/account/#/ios/profile)) - 99€ annuali
2. Rilasciare l'app su App Store Connect tramite [Xcode](https://ionic.io/blog/building-and-releasing-your-capacitor-ios-app)

## Google Maps API

1. Accedere a Google Cloud Console con l'account associato al progetto Google Play ([link](https://console.cloud.google.com/))
2. Creare un nuovo progetto
3. Attivare Google Maps API
4. Ottenere API Key e inserirla nell'app

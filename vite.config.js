import { defineConfig } from 'vite'
import { svelte } from '@sveltejs/vite-plugin-svelte';
import { sveltePreprocess } from 'svelte-preprocess';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@lib': path.resolve(__dirname, './src/lib'),
      '@store': path.resolve(__dirname, './src/store.js')
    },
  },
  plugins: [
    svelte({ preprocess: sveltePreprocess({typescript: false})}),
  ],
  publicDir: './assets/',
  build: {
    outDir: './public/'
  },
  optimizeDeps: { exclude: ["@urql/svelte"] },
})

.height-100 {
    height: 100%;
}

.width-100 {
    width: 100%;
}

ion-text.h1 {
    font-size: 28 !important;
}

ion-text.h2 {
    font-size: 24 !important;
}

ion-text.h3 {
    font-size: 22 !important;
}

ion-text.h4 {
    font-size: 20 !important;
}

ion-text.small {
    font-size: 0.875rem;
}

ion-text.underlined {
    text-decoration: underline !important;
}

a {
    text-decoration: none;
}

/* ion-header {
    box-shadow: none !important;
} */

ion-toolbar {
    --border-width: 0px !important;
}

/* .ion-page > ion-content::before {
    content: "";
    position: absolute;
    
    z-index: 999;
    background-color: transparent;
    height: 50px;
    width: 25px;
    border-top-left-radius: 25px;
    box-shadow: 0 -25px 0 0 var(--ion-color-bg);
}

.ion-page > ion-content::after {
    content: "";
    position: absolute;
    
    right: 0;
    z-index: 999;
    background-color: transparent;
    height: 50px;
    width: 25px;
    border-top-right-radius: 50px;
    box-shadow: 0 -25px 0 0 var(--ion-color-primary);
} */

div.splide.padding {
    padding: 5px 0px;
}

ion-row.section {
    margin-bottom: 16px;
}

ion-card {
    border-radius: 25px;
    box-shadow: 2px 2px 5px gray;
}

ion-card:active {
    filter: brightness(90%);
    transform: translateY(-5px);
}

ion-chip > ion-label {
    font-size: medium !important;
}

ion-label {
    pointer-events: none;
}

ion-chip.ion-color-primary.selected {
    background-color: var(--ion-color-primary);
    color: var(--ion-color-primary-contrast);
}

ion-item:active {
    --background: var(--ion-color-light) !important;
}

ion-item:last-child {
    --inner-border-width: 0;
}

ion-item::part(native) {
    background-color: transparent;
}

ion-searchbar {
    border-radius: 10px;
}

ion-searchbar input {
    border-radius: 10px !important;
}

ion-searchbar > div.searchbar-input-container {
    min-height: 40px !important;
    border: 1px solid gray;
    border-radius: 10px;
    box-shadow: 5px 5px 5px gray;
}

ion-segment {
    box-shadow: 2px 2px 5px gray;
}

ion-segment-button.segment-button-checked {
    background-color: var(--ion-color-primary);
    color: var(--ion-color-primary-contrast);
}

ion-segment-button > ion-label {
    font-size: large;
}

ion-segment-button::part(indicator) {
    display: none;
}

ion-input, ion-textarea, ion-select {
    --border-radius: 12px !important;
    --padding-start: 16px !important;
    --padding-end: 16px !important;
    --highlight-color: var(--ion-color-primary) !important;
}

.alert-wrapper {
    max-width: 80% !important;
}

.alert-message {
    font-size: medium !important;
}
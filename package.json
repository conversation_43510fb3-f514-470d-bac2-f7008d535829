{"name": "il-salento", "productName": "il Salento", "author": "andrea<PERSON>i", "version": "1.0.0", "private": true, "type": "module", "main": "src/main.js", "scripts": {"#Build: Core": "Thesee scripts build the core application (routing logic, and views)", "build:app": "vite build", "#Platform: Build": "These build the application for their specific platforms", "build:android": "run-s build:app cap-run:android", "build:ios": "run-s build:app cap-run:ios", "#Platform: Sync": "These build the application for their specific platforms", "cap-run:android": "cap sync android && cap open android", "cap-run:ios": "cap sync ios && cap open ios", "#Dev: Platform": "These enable HMR (Hot module reloading) directly on the device - please check the readme.md for more on this.", "dev:ios": "run-p dev:start cap-run:ios", "dev:android": "run-p dev:start cap-run:android", "#Dev: Core": "These allow the code to be developed with HMR", "dev:preview": "vite preview", "dev:start": "run-p dev:vite", "dev:vite": "vite --host --port 5001", "#Utilities": "Various utilities", "validate": "svelte-check"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^7.0.0", "@iconify/svelte": "^4.0.2", "@sveltejs/vite-plugin-svelte": "*", "cordova-res": "^0.15.4", "cross-env": "^7.0.3", "npm-run-all": "^4.1.5", "prettier": "^2.2.1", "prettier-plugin-svelte": "^2.2.0", "svelte": "^4.0.0", "svelte-preprocess": "^6.0.0", "vite": "^5.0.0", "vite-aliases": "^0.11.7", "vite-plugin-compression": "^0.2.5"}, "dependencies": {"@capacitor/android": "^7.0.0", "@capacitor/app": "^7.0.0", "@capacitor/browser": "^7.0.0", "@capacitor/core": "^7.0.0", "@capacitor/device": "^7.0.0", "@capacitor/ios": "^7.0.0", "@capacitor/splash-screen": "^7.0.0", "@ionic/core": "^8.3.1", "@splidejs/svelte-splide": "^0.2.9", "@supabase/supabase-js": "^2.45.4", "ionic-svelte": "^0.5.85", "sveaflet": "^0.0.11"}}